import { MiddlewareConsumer, Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AppLoggerMiddleware } from './common/middlewares/app-logger.middleware';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import typeorm from './common/config/typeorm';
import { ScheduleModule } from '@nestjs/schedule';
import { AuthModule } from './auth/auth.module';
import { MailerModule } from '@nestjs-modules/mailer';
import { HandlebarsAdapter } from '@nestjs-modules/mailer/dist/adapters/handlebars.adapter';
import { UserModule } from './user/user.module';
import { TitleModule } from './title/title.module';
import { GroupModule } from './group/group.module';
import { LevelModule } from './level/level.module';
import { LeaveTypeModule } from './leave-type/leave-type.module';
import { DepartmentModule } from './department/department.module';
import { EmployeeTypeModule } from './employee-type/employee-type.module';
import { HolidayModule } from './holiday/holiday.module';
import { WorkShiftModule } from './work-shift/work-shift.module';
import { LeaveModule } from './leave/leave.module';
import { UploadModule } from './upload/upload.module';
import { ApproveListModule } from './approve-list/approve-list.module';
import { LevelTypeModule } from './level-type/level-type.module';
import { LeavePermissionModule } from './leave-permission/leave-permission.module';
import { ReportController } from './report/report.controller';
import { EmployeeModule } from 'src/employee/employee.module';
import { ReportModule } from './report/report.module';
import { EmployeeLeavePermissionModule } from './employee-leave-permission/employee-leave-permission.module';
import { ThrottlerGuard, ThrottlerModule } from '@nestjs/throttler';
import { APP_GUARD } from '@nestjs/core';
import { TasksModule } from './tasks/tasks.module';
import { ServeStaticModule } from '@nestjs/serve-static';
import { join } from 'path';
import { AttandancesModule } from './attandances/attandances.module';
import { OtModule } from './ot/ot.module';
import { ProjectModule } from './project/project.module';
import { OtAirModule } from './ot-air/ot-air.module';
import { ZoneModule } from './zone/zone.module';
import { FloorModule } from './floor/floor.module';
import { CountryModule } from './country/country.module';
import { RecruitModule } from './recruit/recruit.module';
import { CategoryModule } from './category/category.module';
import { CompanyModule } from './company/company.module';
import { CompanyCategoryModule } from './company_category/company_category.module';
import { ActivityModule } from './activity/activity.module';
import { ContactModule } from './contact/contact.module';
import { ContactActivityModule } from './contact_activity/contact_activity.module';
import { ApplicantModule } from './applicant/applicant.module';
import { ApplicantOtpModule } from './applicant_otp/applicant_otp.module';
import { ApplicantFamilyModule } from './applicant_family/applicant_family.module';
import { ApplicantEducationModule } from './applicant_education/applicant_education.module';
import { ApplicantWorkModule } from './applicant_work/applicant_work.module';
import { ApplicantTrainModule } from './applicant_train/applicant_train.module';
import { PersonalFormModule } from './personal-form/personal-form.module';
import { JobModule } from './job/job.module';
import { ApplicantReferenceModule } from './applicant_reference/applicant_reference.module';
import { ApplicantQuestionModule } from './applicant_question/applicant_question.module';
import { ApplicantContactModule } from './applicant_contact/applicant_contact.module';
import { QuestionModule } from './question/question.module';
import { CandidateModule } from './candidate/candidate.module';
import { AppSettingModule } from './app-setting/app-setting.module';
import { CheckInAreaModule } from './check-in-area/check-in-area.module';
import { CompanyForEmployeeModule } from './company-for-employee/company-for-employee.module';


@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [typeorm]
    }),
    TypeOrmModule.forRootAsync({
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => (configService.get('typeorm'))
    }),
    ScheduleModule.forRoot(),
    MailerModule.forRootAsync({
      useFactory: () => ({
        transport: {
          host: process.env.SMTP_HOST,
          port: parseInt(process.env.SMTP_PORT),
          auth: {
            user: process.env.SMTP_USERNAME,
            pass: process.env.SMTP_PASSWORD,
          },
        },
        defaults: {
          from: `"${process.env.SMTP_SENDER}" <${process.env.SMTP_USERNAME}>`,
        },
        template: {
          dir: process.cwd() + '/templates/',
          adapter: new HandlebarsAdapter(),
          options: {
            strict: true,
          },
        },
      }),
    }),
    ServeStaticModule.forRoot({
      rootPath: join(__dirname, '..', 'uploads'),
      serveRoot: '/uploads'
    }),
    // ThrottlerModule.forRoot([{
    //   ttl: 60000,
    //   limit: 10,
    // }]),
    AuthModule,
    UserModule,
    TitleModule,
    GroupModule,
    LevelModule,
    LeaveTypeModule,
    DepartmentModule,
    EmployeeTypeModule,
    HolidayModule,
    EmployeeModule,
    WorkShiftModule,
    LeaveModule,
    UploadModule,
    ApproveListModule,
    LevelTypeModule,
    LeavePermissionModule,
    ReportModule,
    EmployeeModule,
    EmployeeLeavePermissionModule,
    TasksModule,
    AttandancesModule,
    OtModule,
    ProjectModule,
    OtAirModule,
    ZoneModule,
    FloorModule,
    CountryModule,
    RecruitModule,
    CategoryModule,
    CompanyModule,
    CompanyCategoryModule,
    ActivityModule,
    ContactModule,
    ContactActivityModule,
    ApplicantModule,
    ApplicantOtpModule,
    ApplicantFamilyModule,
    ApplicantEducationModule,
    ApplicantWorkModule,
    ApplicantTrainModule,
    PersonalFormModule,
    JobModule,
    ApplicantReferenceModule,
    ApplicantQuestionModule,
    ApplicantContactModule,
    QuestionModule,
    CandidateModule,
    AppSettingModule,
    CheckInAreaModule,
    CompanyForEmployeeModule,
  ],
  controllers: [AppController, ReportController],
  providers: [
    AppService,
    // {
    //   provide: APP_GUARD,
    //   useClass: ThrottlerGuard
    // }
  ],
})
export class AppModule {
  configure(consumer: MiddlewareConsumer): void {
    consumer.apply(AppLoggerMiddleware).forRoutes('/**');
  }
}
