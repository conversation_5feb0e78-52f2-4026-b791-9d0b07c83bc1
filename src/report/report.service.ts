import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Not, Raw, Repository } from 'typeorm';
import { Workbook } from 'exceljs';
import { Employee } from 'src/employee/entities/employee.entity';
import { ViewAttendance } from 'src/attandances/entities/vw-attendance.entity';
import { DateTime } from 'luxon';
import { EmployeeService } from 'src/employee/employee.service';
import { LeaveType } from 'src/leave-type/entities/leave-type.entity';
import { Department } from 'src/department/entities/department.entity';
import { Between, DataSource } from 'typeorm';
import { Attandance } from 'src/attandances/entities/attandance.entity';
import { LeaveService } from 'src/leave/leave.service';
import { log } from 'console';
import { Leave, LeaveStatusEnum } from 'src/leave/entities/leave.entity';
import { Helper } from 'src/common/utils/helper';
import { chain, groupBy, head } from 'lodash';
import { Ot } from 'src/ot/entities/ot.entity';
import { Holiday } from 'src/holiday/entities/holiday.entity';
import { timeout } from 'rxjs';
import { OtReportDto } from './report.controller';
import { OtAir } from 'src/ot-air/entities/ot-air.entity';
import { ViewAttendanceWeb } from 'src/attandances/entities/vw-attendance-web.entity';
import { LeaveDate } from 'src/leave/entities/leave-date.entity';

@Injectable()
export class ReportService {

	constructor(
		@InjectRepository(Employee)
		private employeeRepository: Repository<Employee>,

		@InjectRepository(Department)
		private departmentRepository: Repository<Department>,

		private readonly employeeService: EmployeeService,
		private readonly leaveService: LeaveService
	) { }

	async exportEmployee() {
		const workbook = new Workbook();
		const worksheet = workbook.addWorksheet('employee');

		// Add headers
		worksheet.addRow(['code', 'firstname', 'lastname', 'birthDate', 'registerDate', 'passProbationDate', 'employeeType', 'active', 'head']);

		// Fetch employee data from the database
		const employees = await this.employeeRepository.find({
			relations: ['head', 'employeeType'],
		});
		// Populate rows with employee data
		employees.forEach(employee => {
			worksheet.addRow([
				employee.code,
				employee.firstname,
				employee.lastname,
				employee.birthDate,
				employee.registerDate,
				employee.passProbationDate,
				employee.employeeType ? employee.employeeType.name : null,
				employee.active,
				employee.head ? employee.head.firstname : null
			]);
		});

		// Write data to buffer and return
		return workbook.xlsx.writeBuffer();
	}

	async viewDailyRawTransactions(date: string, employeeId: number, departmentId: number, type: 'REPORT' | 'EXCEL') {


		let whereConditions: any = {
			//
		};

		if (date) {
			whereConditions = {
				...whereConditions,
				date: date
			};
		}
		if (employeeId || departmentId) {
			whereConditions = {
				...whereConditions,
				employee: {
					...(employeeId && { id: employeeId }),
					...(departmentId && { department: { id: departmentId } }),
				},
			};
		}

		const result = await ViewAttendance.find({
			where: whereConditions,
			order: {
				code: 'ASC',
				date: 'ASC'
			}
		});

		if (type === 'REPORT') {
			return { message: 'ok', data: result }
		}

		const workbook = new Workbook();
		const worksheet = workbook.addWorksheet('Attendance');

		// Add headers
		const headers = ['User ID', 'User Name', 'Date', 'Time In', 'Time Out', 'Calc. Hrs'];
		const headerRow = worksheet.addRow(headers);

		// Style the header row
		headerRow.eachCell((cell, colNumber) => {
			cell.fill = {
				type: 'pattern',
				pattern: 'solid',
				fgColor: { argb: 'D3D3D3' }, // Light gray
			};
			cell.font = {
				bold: true,
			};
			cell.alignment = {
				horizontal: 'center',
				vertical: 'middle',
			};
		});

		result.sort((a, b) => {
			const nameA = `${a?.firstname || ''} ${a?.lastname || ''}`.toLowerCase();
			const nameB = `${b?.firstname || ''} ${b?.lastname || ''}`.toLowerCase();
			return nameA.localeCompare(nameB);
		});

		result.forEach(item => {
			worksheet.addRow([
				item.code,
				`${item.firstname} ${item.lastname}`,
				DateTime.fromJSDate(new Date(item.date)).toLocal().toFormat('dd/MM/yyyy'),
				item.min.slice(0, 5),
				item.max.slice(0, 5),
				item.calhrs
			]);
		});

		// Adjust column widths based on the longest content in each column
		worksheet.columns.forEach((column, index) => {
			let maxLength = headers[index]?.length || 10; // Start with the header length or a default width
			column.eachCell({ includeEmpty: true }, (cell) => {
				if (cell.value) {
					const cellValue = cell.value.toString();
					maxLength = Math.max(maxLength, cellValue.length);
				}
			});
			column.width = maxLength + 2; // Add some padding for better appearance
		});

		// Write data to buffer and return
		return workbook.xlsx.writeBuffer();
	}

	async viewDailyRawTransactionsWeb(date: string, employeeId: number, departmentId: number, type: 'REPORT' | 'EXCEL') {
		let whereConditions: any = {
			//
		};

		if (date) {
			whereConditions = {
				...whereConditions,
				date: date
			};
		}
		if (employeeId || departmentId) {
			whereConditions = {
				...whereConditions,
				employee: {
					...(employeeId && { id: employeeId }),
					...(departmentId && { department: { id: departmentId } }),
				},
			};
		}

		const result = await ViewAttendanceWeb.find({
			where: {
				...whereConditions,
			},
			order: {
				code: 'ASC',
				date: 'ASC'
			}
		});

		return { message: 'ok', data: result }
	}

	async timeAttendanceViewByUser(year: number, month: number, employeeId: number) {
		const { data } = await this.employeeService.getEmployeeAttdance1(employeeId, year, month)

		const leaveTypes = (await LeaveType.find({ order: { code: 'ASC' } })).map(leaveType => ({
			field: leaveType.name.slice(0, 3),
			type: 'text',
			label: leaveType.name
		}))

		const fields = [
			{ field: 'date', type: 'text', label: 'Date' },
			{ field: 'day', type: 'text', label: 'Day' },
			{ field: 'timeIn', type: 'text', label: 'Time In' },
			{ field: 'timeOut', type: 'text', label: 'Time Out' },
			{ field: 'calcHrs', type: 'text', label: 'Calc. Hrs Total' },
			{ field: 'workHrs', type: 'text', label: 'Work Hrs' },
			...leaveTypes
		]

		return { fields: fields, data: data }
	}


	async monthlyLeaveReportExcel(year: string, month: string, departmentId: Array<number>, type: 'REPORT' | 'EXCEL') {

		let dateStart = null
		let dateEnd = null

		if (month && year) {
			const paddedMonth = month.padStart(2, '0');

			// สร้าง dateStart และ dateEnd
			dateStart = `${year}-${paddedMonth}-01`;

			// ใช้ JavaScript Date เพื่อหา dateEnd
			const endDate = new Date(Number(year) + 1, Number(month), 0);
			dateEnd = `${year}-${paddedMonth}-${endDate
				.getDate()
				.toString()
				.padStart(2, '0')}`;

		}

		// console.log({ dateStart, dateEnd });

		let whereConditions: any = {
			//
		};

		if (departmentId) {
			whereConditions = {
				...whereConditions,
				department: {
					id: In(departmentId),
				}
			};
		}

		if (month && year) {
			whereConditions = {
				...whereConditions,
				leaves: {
					// กรองกรณีที่วันที่เริ่มต้นและสิ้นสุดอยู่ในช่วงที่ระบุ
					dateStart: Raw(alias => `${alias} BETWEEN :dateStart AND :dateEnd`, { dateStart, dateEnd }),
					dateEnd: Raw(alias => `${alias} BETWEEN :dateStart AND :dateEnd`, { dateStart, dateEnd }),
					status: Not(In([LeaveStatusEnum.REJECT, LeaveStatusEnum.CANCEL])),
				}
			};
		}


		const employee = await this.employeeRepository.find({
			relations: ['leaves.leaveType', 'department'],
			where: whereConditions
		});

		let _department = []
		for (const em of employee) {
			//push department
			_department.push({
				id: em.department.id,
				name: em.department.name,
				employee: []
			});
		}

		// ใส่ employee เข้าไปใน array employee: []
		for (const _dept of _department) {
			for (const em of employee) {
				if (_dept.id === em.department.id) {
					_dept.employee.push(em);
				}
			}
		}

		// return _department

		if (type === 'REPORT') {
			return { message: 'ok', data: _department }
		}

		const workbook = new Workbook();
		const worksheet = workbook.addWorksheet('Accumulate Staff');

		// Add headers
		const headers = ['NAME', 'FORM', 'TO', 'LEAVE TYPE', 'LEAVE TOTAL', 'ENTITLEMENT', 'TAKEN', 'REMAIN', 'EXCESS', 'STATUS'];
		const headerRow = worksheet.addRow(headers);

		// Style the header row
		headerRow.eachCell((cell, colNumber) => {
			cell.fill = {
				type: 'pattern',
				pattern: 'solid',
				fgColor: { argb: 'D3D3D3' }, // Light gray
			};
			cell.font = {
				bold: true,
			};
			cell.alignment = {
				horizontal: 'center',
				vertical: 'middle',
			};
		});

		_department.forEach((item) => {

			const row = worksheet.addRow([item.name, '', '', '', '', '', '', '', '', '']);

			row.eachCell((cell, colNumber) => {
				cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFDBEAFE' } };
			});

			item.employee.forEach((employee) => {

				const row = worksheet.addRow([employee.firstname + ' ' + employee.lastname, '', '', '', '', '', '', '', '', '']);
				row.eachCell((cell, colNumber) => {
					cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'fef9c3' } };
				});

				employee.leaves.forEach((leave) => {
					// จำนวนการลา
					worksheet.addRow([
						'',
						leave.dateStart,
						leave.dateEnd,
						leave.leaveType.name,
						leave.qtyDay,
						leave.entitlementDay,
						leave.takenDay,
						leave.remainDay,
						leave.excessDay,
						leave.status,
					]);
				});

			});
		});

		// Adjust column widths based on the longest content in each column
		worksheet.columns.forEach((column, index) => {
			let maxLength = headers[index]?.length || 10; // Start with the header length or a default width
			column.eachCell({ includeEmpty: true }, (cell) => {
				if (cell.value) {
					const cellValue = cell.value.toString();
					maxLength = Math.max(maxLength, cellValue.length);
				}
			});
			column.width = maxLength + 2; // Add some padding for better appearance
		});

		// Write data to buffer and return
		return workbook.xlsx.writeBuffer();
	}

	async reportAccumulateStaff(body) {

		const month = body.month
		const year = body.year
		const departmentId = body.departmentId
		const employeeId = body.employeeId

		let dateStart = null
		let dateEnd = null

		// if (!year) {
		// 	throw new BadRequestException("please input year")
		// }

		if (year && !month) {
			dateStart = `${year}-01-01`;
			dateEnd = `${year}-12-31`;

		} else if (year && month) {
			const paddedMonth = month.padStart(2, '0');

			// สร้าง dateStart และ dateEnd
			dateStart = `${year}-${paddedMonth}-01`;

			// ใช้ JavaScript Date เพื่อหา dateEnd
			const endDate = new Date(Number(year) + 1, Number(month), 0);
			dateEnd = `${year}-${paddedMonth}-${endDate
				.getDate()
				.toString()
				.padStart(2, '0')}`;

		}

		// console.log({ dateStart, dateEnd });
		let _department = [];

		let department;

		if (departmentId.length > 0) {
			department = await this.departmentRepository.find({
				where: {
					id: In(departmentId),
				},
			});
		} else {
			department = await this.departmentRepository.find();
		}

		for (const dep of department) {

			let whereConditions: any = {
				department: {
					id: dep.id,
				},
			};

			if (employeeId) {
				whereConditions = {
					...whereConditions,
					id: employeeId
				};
			}

			if (year) {
				whereConditions = {
					...whereConditions,
					employeeLeavePermissions: {
						startPeriod: Raw(alias => `to_char(${alias}, 'YYYY') LIKE :year`, { year: `${year}%` }),
					},
				};
			}

			const employee = await this.employeeRepository.find({
				relations: ['employeeLeavePermissions.leaveType'],
				where: whereConditions,
				order: {
					employeeLeavePermissions: {
						leaveType: {
							code: 'ASC'
						}
					}
				}
			});

			for (const em of employee) {

				for (const employeeLeavePermission of em.employeeLeavePermissions) {

					//get filter
					// const getLeavePermissionRemain = await this.leaveService.getLeavePermissionRemain(em.id, employeeLeavePermission.leaveType.id, year, dateStart, dateEnd)
					// let _leave: any = {
					// 	Entitlement: getLeavePermissionRemain.entitlementDay,//สิทธิการลา
					// 	Taken: getLeavePermissionRemain.takenDay,//จำนวนที่ใช้ไป
					// 	Remain: getLeavePermissionRemain.remainDay,
					// 	Excess: getLeavePermissionRemain.excessCurrentLeaveDay //จำนวนวันที่ได้ excess ปัจจุบัน
					// };

					//get ปัจจุบัน
					let _leave: any = {
						Entitlement: employeeLeavePermission.qtyDay,//สิทธิการลา
						Taken: employeeLeavePermission.usedDay,//จำนวนที่ใช้ไป
						Remain: employeeLeavePermission.remain,
						Excess: employeeLeavePermission.excessDay //จำนวนวันที่ได้ excess ปัจจุบัน
					};

					employeeLeavePermission.leavePermission = _leave;
				}
			}
			//

			if (employee.length > 0) {
				//push department
				_department.push({
					id: dep.id,
					name: dep.name,
					employee: employee
				});
			}

		}

		return _department
	}

	async detailAccumulateStaff(body) {

		const month = body.month
		const year = body.year
		const departmentId = body.departmentId
		const employeeId = body.employeeId
		const leaveTypeId = body.leaveTypeId

		let dateStart = body.dateStart
		let dateEnd = body.dateEnd

		if (dateStart && dateEnd) {
			dateStart = body.dateStart
			dateEnd = body.dateEnd
		} else {
			dateStart = null
			dateEnd = null
		}

		// if (!year) {
		// 	throw new BadRequestException("please input year")
		// }

		// if (year && !month) {
		// 	dateStart = `${year}-01-01`;
		// 	dateEnd = `${year}-12-31`;

		// } else if (year && month) {
		// 	const paddedMonth = month.padStart(2, '0');

		// 	// สร้าง dateStart และ dateEnd
		// 	dateStart = `${year}-${paddedMonth}-01`;

		// 	// ใช้ JavaScript Date เพื่อหา dateEnd
		// 	const endDate = new Date(Number(year) + 1, Number(month), 0);
		// 	dateEnd = `${year}-${paddedMonth}-${endDate
		// 		.getDate()
		// 		.toString()
		// 		.padStart(2, '0')}`;

		// }

		// console.log({ dateStart, dateEnd });

		let whereConditions: any = {
			//
		};


		if (employeeId) {
			whereConditions = {
				...whereConditions,
				id: employeeId
			};
		}

		if (departmentId.length > 0) {
			whereConditions = {
				...whereConditions,
				department: {
					id: In(departmentId),
				}
			};
		}

		// if (month && year) {
		// 	whereConditions = {
		// 		...whereConditions,
		// 		leaves: {
		// 			// กรองกรณีที่วันที่เริ่มต้นและสิ้นสุดอยู่ในช่วงที่ระบุ
		// 			dateStart: Raw(alias => `${alias} BETWEEN :dateStart AND :dateEnd`, { dateStart, dateEnd }),
		// 			dateEnd: Raw(alias => `${alias} BETWEEN :dateStart AND :dateEnd`, { dateStart, dateEnd }),
		// 			status: Not(In([LeaveStatusEnum.REJECT, LeaveStatusEnum.CANCEL])),
		// 		}
		// 	};
		// }

		if (dateStart && dateEnd) {
			whereConditions = {
				...whereConditions,
				leaves: {
					...whereConditions.leaves, // รวมเงื่อนไขก่อนหน้าถ้ามี
					dateStart: Raw(alias => `${alias} BETWEEN :dateStart AND :dateEnd`, { dateStart, dateEnd }),
					dateEnd: Raw(alias => `${alias} BETWEEN :dateStart AND :dateEnd`, { dateStart, dateEnd }),
					// status: Not(In([LeaveStatusEnum.REJECT, LeaveStatusEnum.CANCEL])),
				},
			};
		}

		if (leaveTypeId) {
			whereConditions = {
				...whereConditions,
				leaves: {
					...whereConditions.leaves, // รวมเงื่อนไขก่อนหน้าถ้ามี
					leaveType: {
						id: leaveTypeId,
					},
				},
			};
		}


		whereConditions = {
			...whereConditions,
			leaves: {
				...whereConditions.leaves, // รวมเงื่อนไขก่อนหน้าถ้ามี
				status: Not(In(['cancel', 'reject'])),
			},
		};

		const employees = await this.employeeRepository.find({
			relations: ['leaves.leaveType', 'department'],
			where: whereConditions,
			order: {
				leaves: {
					code: 'ASC'
				}
			}
		});

		const filteredEmployees = employees.filter(emp => emp.leaves && emp.leaves.length > 0);

		return filteredEmployees
	}


	async monthlyLeaveReport(body) {

		const month = body.month
		const year = body.year
		const departmentId = body.departmentId
		const employeeId = body.employeeId

		let dateStart = null
		let dateEnd = null

		if (!year) {
			throw new BadRequestException("please input year")
		}

		if (year && !month) {
			dateStart = `${year}-01-01`;
			dateEnd = `${year}-12-31`;

		} else if (year && month) {
			const paddedMonth = month.padStart(2, '0');

			// สร้าง dateStart และ dateEnd
			dateStart = `${year}-${paddedMonth}-01`;

			// ใช้ JavaScript Date เพื่อหา dateEnd
			const endDate = new Date(Number(year) + 1, Number(month), 0);
			dateEnd = `${year}-${paddedMonth}-${endDate
				.getDate()
				.toString()
				.padStart(2, '0')}`;

		}

		// console.log({ dateStart, dateEnd });

		let whereConditions: any = {
			//
		};

		if (employeeId) {
			whereConditions = {
				...whereConditions,
				id: employeeId,
			};
		}

		if (departmentId.length > 0) {
			whereConditions = {
				...whereConditions,
				department: {
					id: In(departmentId),
				}
			};
		}

		if (month && year) {
			whereConditions = {
				...whereConditions,
				leaves: {
					...whereConditions.leaves, // รวมเงื่อนไขเดิมใน leaves ถ้ามี
					// กรองกรณีที่วันที่เริ่มต้นและสิ้นสุดอยู่ในช่วงที่ระบุ
					dateStart: Raw(alias => `${alias} BETWEEN :dateStart AND :dateEnd`, { dateStart, dateEnd }),
					dateEnd: Raw(alias => `${alias} BETWEEN :dateStart AND :dateEnd`, { dateStart, dateEnd }),
					status: Not(In([LeaveStatusEnum.REJECT, LeaveStatusEnum.CANCEL])),
				}
			};
		}


		const employee = await this.employeeRepository.find({
			relations: ['leaves.leaveType', 'department'],
			where: whereConditions
		});

		let _department = []
		for (const em of employee) {
			//push department
			_department.push({
				id: em.department.id,
				name: em.department.name,
				employee: []
			});
		}

		// ใช้ groupBy เพื่อจัดกลุ่มตาม id
		_department = Object.values(groupBy(_department, 'id')).map(group => group[0]);

		// ใส่ employee เข้าไปใน array employee: []
		for (const _dept of _department) {
			for (const em of employee) {
				if (_dept.id === em.department.id) {
					_dept.employee.push(em);
				}
			}
		}

		return _department
	}

	async dailyAttendanceReport(body) {

		const dateStart = body.dateStart
		const dateEnd = body.dateEnd
		const employeeId = body.employeeId
		const departmentId = body.departmentId

		if (!dateStart || !dateEnd) {
			throw new BadRequestException("please input dateStart and dateEnd")
		}


		let whereConditions: any = {
			//
		};

		if (employeeId) {
			whereConditions = {
				...whereConditions,
				employee: {
					...whereConditions.employee,
					id: employeeId
				}
			};
		}

		if (departmentId.length > 0) {
			whereConditions = {
				...whereConditions,
				employee: {
					...whereConditions.employee,
					department: {
						id: In(departmentId),
					}
				}
			};
		}

		if (dateStart && dateEnd) {

			const dateStartFormat = Helper.formatDate(`${dateStart}`);
			const dateEndFormat = Helper.formatDate(`${dateEnd}`);

			whereConditions.date = Raw(alias => `${alias} BETWEEN :dateStart AND :dateEnd`, { dateStart: dateStartFormat, dateEnd: dateEndFormat });
		}
		const result = await ViewAttendance.find({
			relations: ['employee.department'],
			where: whereConditions,
			order: {
				code: 'ASC',
				date: 'ASC'
			}
		});

		return result
	}

	async monthlyAttendanceReport(body) {

		const month = body.month
		const year = body.year
		const employeeId = body.employeeId
		const departmentId = body.departmentId

		let dateStart = null
		let dateEnd = null

		if (!year) {
			throw new BadRequestException("please input year")
		}

		if (year && !month) {
			dateStart = `${year}-01-01`;
			dateEnd = `${year}-12-31`;

		} else if (year && month) {
			const paddedMonth = month.padStart(2, '0');

			// สร้าง dateStart และ dateEnd
			dateStart = `${year}-${paddedMonth}-01`;

			// ใช้ JavaScript Date เพื่อหา dateEnd
			const endDate = new Date(Number(year) + 1, Number(month), 0);
			dateEnd = `${year}-${paddedMonth}-${endDate
				.getDate()
				.toString()
				.padStart(2, '0')}`;

		}


		let whereConditions: any = {
			//
		};

		if (employeeId) {
			whereConditions = {
				...whereConditions,
				employee: {
					...whereConditions.employee,
					head: {
						id: employeeId,
					},
				},
			};
		}

		if (departmentId && departmentId.length > 0) {
			whereConditions = {
				...whereConditions,
				employee: {
					...whereConditions.employee,
					department: {
						id: In(departmentId),
					},
				},
			};
		}

		if (dateStart && dateEnd) {

			const dateStartFormat = Helper.formatDate(`${dateStart}`);
			const dateEndFormat = Helper.formatDate(`${dateEnd}`);

			whereConditions = {
				...whereConditions,
				employee: {
					...whereConditions.employee,
				},
				workDate: Raw(alias => `${alias} BETWEEN :dateStart AND :dateEnd`, { dateStart: dateStartFormat, dateEnd: dateEndFormat })
			};


		}

		const result = await Attandance.find({
			relations: ['employee.department'],
			where: whereConditions
		});

		let qtyHour = 0.0
		let qtyHourExpect = 0.0
		for (const r of result) {
			qtyHour = Helper.convertMinutesToHours(r.workedHours)
			qtyHourExpect = Helper.convertMinutesToHours(r.expectedHours)

			r.calc_hrs = qtyHourExpect
			r.work_hrs = qtyHour

			////////////////// get leave on day////////////////////
			const leaveDays = await LeaveDate.find({
				relations: ['leave', 'leave.leaveType'],
				where: {
					leave: {
						employee: {
							id: r.employee.id,
						},
						status: (In([LeaveStatusEnum.APPROVED, LeaveStatusEnum.HR_APPROVED])),
					},
					date: Raw(alias => `${alias} = :date`, { date: r.workDate }),
				},
			});

			const leaves = leaveDays.map(leaveDate => leaveDate.leave);

			//get column
			const leaveTypes = await LeaveType.find();
			const _leaveType = leaveTypes.map(e => ({ id: e.id, code: e.code, name: e.name, qty: 0 }));

			//check leave
			for (const leave of leaves) {
				for (const _LT of _leaveType) {
					if (leave.leaveType.id == _LT.id) {
						_LT.qty = 1;
						break;
					}
				}
			}

			r.column_leave_type = _leaveType
			//////////////////////////////////////////////////////
		}


		return result
	}


	async summaryDeductionReport(body) {

		const month = body.month
		const year = body.year
		const departmentId = body.departmentId
		const headId = body.headId;

		let dateStart = null
		let dateEnd = null

		if (!year) {
			throw new BadRequestException("please input year")
		}

		if (year && !month) {
			dateStart = `${year}-01-01`;
			dateEnd = `${year}-12-31`;

		} else if (year && month) {
			const paddedMonth = month.padStart(2, '0');

			dateStart = `${year}-${paddedMonth}-01`;

			const endDate = new Date(Number(year), Number(month), 0);
			dateEnd = `${year}-${paddedMonth}-${endDate
				.getDate()
				.toString()
				.padStart(2, '0')}`;
		}

		let _department = [];

		let department;

		if (departmentId.length > 0) {
			department = await this.departmentRepository.find({
				where: {
					id: In(departmentId),
				},
			});
		} else {
			department = await this.departmentRepository.find();
		}

		for (const dep of department) {

			//employee in dept
			const employee = await this.employeeRepository.find({
				relations: [],
				where: {
					department: {
						id: dep.id,
					},
					head: {
						id: headId
					}
				},
			});

			for (const em of employee) {

				//attendance

				let whereConditions: any = {
					employee: {
						id: em.id
					}
				};

				if (dateStart && dateEnd) {

					const dateStartFormat = Helper.formatDate(`${dateStart}`);
					const dateEndFormat = Helper.formatDate(`${dateEnd}`);

					whereConditions.workDate = Raw(alias => `${alias} BETWEEN :dateStart AND :dateEnd`, { dateStart: dateStartFormat, dateEnd: dateEndFormat });
				}

				const result = await Attandance.find({
					relations: ['employee'],
					where: {
						...whereConditions,
						deduct: true
					}
				});

				// กรองเฉพาะแถวที่ deduct เป็น false (f)
				const filteredResult = result.filter(row => row.deduct === true);
				console.log(filteredResult);

				// case1
				const case1 = filteredResult.filter(row => row.case1 > 0);
				const sumCase1 = case1.reduce((sum, row) => sum + row.case1, 0);

				// case2
				const case2 = filteredResult.filter(row => row.case2 > 0);
				const sumCase2 = case2.reduce((sum, row) => sum + row.case2, 0);

				// case3
				const case3 = filteredResult.filter(row => row.case3 > 0);
				const sumCase3 = case3.reduce((sum, row) => sum + row.case3, 0);

				// case4
				const case4 = filteredResult.filter(row => row.case4 > 0);
				const sumCase4 = case4.reduce((sum, row) => sum + row.case4, 0);

				//total deduct
				//case 1 = Late > 30 Mins , case 2 = Late > 60 Mins
				//case 3 = Late > 90 Mins , case 4 = Late > 150 Mins

				const total_deduct_min = (sumCase1 * 30) + (sumCase2 * 60) + (sumCase3 * 90) + (sumCase4 * 150)

				em.total_case1 = sumCase1
				em.total_case2 = sumCase2
				em.total_case3 = sumCase3
				em.total_case4 = sumCase4
				em.total_deduct_min = total_deduct_min
			}

			//push department
			_department.push({
				id: dep.id,
				name: dep.name,
				employee: employee
			});
		}

		return _department
	}


	async otRequestReport(body) {

		const dateStart = body.dateStart
		const dateEnd = body.dateEnd
		const employeeId = body.employeeId
		const departmentId = body.departmentId

		const headId = body.headId
		const approverId = body.approverId

		const projectId = body.projectId
		const projectOwnerId = body.projectOwnerId

		const status = body.status

		if (!dateStart || !dateEnd) {
			throw new BadRequestException("please input dateStart and dateEnd")
		}


		let whereConditions: any = {
			status: Not(In(['cancel', 'reject'])),
		};

		if (status) {
			whereConditions = {
				...whereConditions,
				status: status
			};
		}

		if (employeeId) {
			whereConditions = {
				...whereConditions,
				employee: {
					...(whereConditions.employee || {}), // รักษาเงื่อนไขเดิมของ employee
					id: employeeId,
				},
			};
		}

		if (headId) {
			whereConditions = {
				...whereConditions,
				head: {
					...(whereConditions.head || {}), // รักษาเงื่อนไขเดิมของ head
					id: headId,
				},
			};
		}

		if (approverId) {
			whereConditions = {
				...whereConditions,
				approver: {
					...(whereConditions.approver || {}), // รักษาเงื่อนไขเดิมของ approver
					id: approverId,
				},
			};
		}

		if (projectId) {
			whereConditions = {
				...whereConditions,
				project: {
					...(whereConditions.project || {}), // รักษาเงื่อนไขเดิมของ project
					id: projectId,
				},
			};
		}

		if (projectOwnerId) {
			whereConditions = {
				...whereConditions,
				project: {
					...(whereConditions.project || {}), // รักษาเงื่อนไขเดิมของ project
					employee: {
						...(whereConditions.project?.employee || {}), // รักษาเงื่อนไขเดิมของ project.employee
						id: projectOwnerId,
					},
				},
			};
		}

		if (departmentId.length > 0) {
			whereConditions = {
				...whereConditions,
				employee: {
					...(whereConditions.employee || {}), // รักษาเงื่อนไขเดิมของ employee
					department: {
						...(whereConditions.employee?.department || {}), // รักษาเงื่อนไขเดิมของ department
						id: In(departmentId),
					},
				},
			};
		}

		if (dateStart && dateEnd) {
			const dateStartFormat = Helper.formatDate(`${dateStart}`);
			const dateEndFormat = Helper.formatDate(`${dateEnd}`);

			whereConditions.date = Raw(
				alias => `${alias} BETWEEN :dateStart AND :dateEnd`,
				{ dateStart: dateStartFormat, dateEnd: dateEndFormat }
			);
		}

		const ots = await Ot.find({
			relations: ['employee.department', 'employee.employeeType', 'head.department', 'project', 'approver'],
			where: whereConditions,
			order: {
				code: 'ASC',
				date: 'ASC'
			}
		});

		for (const ot of ots) {

			let is_holiday = false
			let is_issue = true

			//check holiday
			const formattedDate = ot?.date
				? DateTime.fromSQL(ot.date.toString()).toLocal().toFormat('yyyy-MM-dd')
				: null;

			const getWorkShiftEmployee = await this.employeeService.getWorkShiftEmployee(employeeId, formattedDate)
			if (getWorkShiftEmployee) {
				const _is_holiday = getWorkShiftEmployee.is_holiday
				is_holiday = _is_holiday
			}
			//


			ot.isHoliday = is_holiday
			ot.isIssue = is_issue
		}

		return ots
	}

	async otAirRequestReport(body) {

		const dateStart = body.dateStart
		const dateEnd = body.dateEnd
		const employeeId = body.employeeId
		const departmentId = body.departmentId

		const headId = body.headId
		const approverId = body.approverId

		const projectId = body.projectId
		const projectOwnerId = body.projectOwnerId

		const status = body.status

		if (!dateStart || !dateEnd) {
			throw new BadRequestException("please input dateStart and dateEnd")
		}


		let whereConditions: any = {
			status: Not(In(['cancel', 'reject'])),
		};

		if (status) {
			whereConditions = {
				...whereConditions,
				status: status
			};
		}

		if (employeeId) {
			whereConditions = {
				...whereConditions,
				employee: {
					...(whereConditions.employee || {}), // รักษาเงื่อนไขเดิมของ employee
					id: employeeId,
				},
			};
		}

		if (headId) {
			whereConditions = {
				...whereConditions,
				head: {
					...(whereConditions.head || {}), // รักษาเงื่อนไขเดิมของ head
					id: headId,
				},
			};
		}

		if (approverId) {
			whereConditions = {
				...whereConditions,
				approver: {
					...(whereConditions.approver || {}), // รักษาเงื่อนไขเดิมของ approver
					id: approverId,
				},
			};
		}

		if (projectId) {
			whereConditions = {
				...whereConditions,
				project: {
					...(whereConditions.project || {}), // รักษาเงื่อนไขเดิมของ project
					id: projectId,
				},
			};
		}

		if (projectOwnerId) {
			whereConditions = {
				...whereConditions,
				project: {
					...(whereConditions.project || {}), // รักษาเงื่อนไขเดิมของ project
					employee: {
						...(whereConditions.project?.employee || {}), // รักษาเงื่อนไขเดิมของ project.employee
						id: projectOwnerId,
					},
				},
			};
		}

		if (departmentId.length > 0) {
			whereConditions = {
				...whereConditions,
				employee: {
					...(whereConditions.employee || {}), // รักษาเงื่อนไขเดิมของ employee
					department: {
						...(whereConditions.employee?.department || {}), // รักษาเงื่อนไขเดิมของ department
						id: In(departmentId),
					},
				},
			};
		}

		if (dateStart && dateEnd) {
			const dateStartFormat = Helper.formatDate(`${dateStart}`);
			const dateEndFormat = Helper.formatDate(`${dateEnd}`);

			whereConditions.date = Raw(
				alias => `${alias} BETWEEN :dateStart AND :dateEnd`,
				{ dateStart: dateStartFormat, dateEnd: dateEndFormat }
			);
		}

		const ots = await OtAir.find({
			relations: ['employee.department', 'employee.employeeType', 'head.department', 'project', 'approver'],
			where: whereConditions,
			order: {
				code: 'ASC',
				date: 'ASC'
			}
		});

		for (const ot of ots) {

			let is_holiday = false
			let is_issue = true

			//check holiday
			const formattedDate = ot?.date
				? DateTime.fromSQL(ot.date.toString()).toLocal().toFormat('yyyy-MM-dd')
				: null;

			const getWorkShiftEmployee = await this.employeeService.getWorkShiftEmployee(employeeId, formattedDate)
			if (getWorkShiftEmployee) {
				const _is_holiday = getWorkShiftEmployee.is_holiday
				is_holiday = _is_holiday
			}
			//


			ot.isHoliday = is_holiday
			ot.isIssue = is_issue
		}

		return ots
	}

	async otComparisonReport(body: OtReportDto) {
		const { dateStart, dateEnd, employeeId, headId, projectId, approverId, projectOwnerId, departmentId, status } = body

		if (!dateStart || !dateEnd) {
			throw new BadRequestException("please input dateStart and dateEnd")
		}


		let whereConditions: any = {
			status: Not(In(['cancel', 'reject'])),
		};

		if (status) {
			whereConditions = {
				...whereConditions,
				status: status
			};
		}

		if (employeeId) {
			whereConditions = {
				...whereConditions,
				employee: {
					...(whereConditions.employee || {}), // รักษาเงื่อนไขเดิมของ employee
					id: employeeId,
				},
			};
		}


		if (headId) {
			whereConditions = {
				...whereConditions,
				head: {
					...(whereConditions.head || {}), // รักษาเงื่อนไขเดิมของ head
					id: headId,
				},
			};
		}

		if (approverId) {
			whereConditions = {
				...whereConditions,
				approver: {
					...(whereConditions.approver || {}), // รักษาเงื่อนไขเดิมของ approver
					id: approverId,
				},
			};
		}

		if (projectId) {
			whereConditions = {
				...whereConditions,
				project: {
					...(whereConditions.project || {}), // รักษาเงื่อนไขเดิมของ project
					id: projectId,
				},
			};
		}

		if (projectOwnerId) {
			whereConditions = {
				...whereConditions,
				project: {
					...(whereConditions.project || {}), // รักษาเงื่อนไขเดิมของ project
					employee: {
						...(whereConditions.project?.employee || {}), // รักษาเงื่อนไขเดิมของ project.employee
						id: projectOwnerId,
					},
				},
			};
		}

		if (departmentId.length > 0) {
			whereConditions = {
				...whereConditions,
				employee: {
					...(whereConditions.employee || {}), // รักษาเงื่อนไขเดิมของ employee
					department: {
						...(whereConditions.employee?.department || {}), // รักษาเงื่อนไขเดิมของ department
						id: In(departmentId),
					},
				},
			};
		}

		if (dateStart && dateEnd) {
			const dateStartFormat = Helper.formatDate(`${dateStart}`);
			const dateEndFormat = Helper.formatDate(`${dateEnd}`);

			whereConditions.date = Raw(
				alias => `${alias} BETWEEN :dateStart AND :dateEnd`,
				{ dateStart: dateStartFormat, dateEnd: dateEndFormat }
			);
		}

		const ots = await Ot.find({
			where: whereConditions,
			relations: {
				employee: {
					vwAttendance: true,
				},
				approver: true,
				project: true
			},
			order: {
				code: 'ASC',
				date: 'ASC'
			}
		})

		for (const ot of ots) {

			let is_holiday = false
			let is_issue = true

			//check holiday
			const formattedDate = ot?.date
				? DateTime.fromSQL(ot.date.toString()).toLocal().toFormat('yyyy-MM-dd')
				: null;

			const getWorkShiftEmployee = await this.employeeService.getWorkShiftEmployee(employeeId, formattedDate)
			if (getWorkShiftEmployee) {
				const _is_holiday = getWorkShiftEmployee.is_holiday
				is_holiday = _is_holiday
			}
			//

			ot.isHoliday = is_holiday
		}


		const groupByEmployee = chain(ots)
			.groupBy((ot) => {
				return ot.employee.code + '-' + ot.employee.fullname
			}).map((v, k) => {
				for (const ot of v) {
					const date = ot.date;

					const targetDate = new Date(date).setHours(0, 0, 0, 0);

					const day = ot.employee.vwAttendance.find((e) => {
						if (!e.date) {
							console.log('Skipping attendance with no date:', e);
							return false;
						}

						const attendanceDate = new Date(e.date).setHours(0, 0, 0, 0);
						console.log(`Comparing ${attendanceDate} with ${targetDate}`);

						return attendanceDate === targetDate;
					});

					if (day) {
						ot['in'] = day.min;
						ot['out'] = day.max;
					} else {
						ot['in'] = null;
						ot['out'] = null;
					}

				}

				const code = k.split('-')

				return ({
					code: code[0],
					name: code[1],
					ots: v,
					total: v.reduce((sum, curr) => sum + curr.qtyHour, 0)
				});
			}).value();

		for (const groups of groupByEmployee) {
			for (const ot of groups.ots) {
				delete ot.employee.vwAttendance
			}
		}

		return groupByEmployee
	}

	async otRequestGroupByTeam(body: OtReportDto) {
		const { dateStart, dateEnd, employeeId, headId, projectId, approverId, projectOwnerId, departmentId, status } = body

		if (!dateStart || !dateEnd) {
			throw new BadRequestException("please input dateStart and dateEnd")
		}

		let whereConditions: any = {
			status: Not(In(['cancel', 'reject'])),
		};

		if (status) {
			whereConditions = {
				...whereConditions,
				status: status
			};
		}

		if (employeeId) {
			whereConditions = {
				...whereConditions,
				employee: {
					...(whereConditions.employee || {}), // รักษาเงื่อนไขเดิมของ employee
					id: employeeId,
				},
			};
		}

		if (headId) {
			whereConditions = {
				...whereConditions,
				head: {
					...(whereConditions.head || {}), // รักษาเงื่อนไขเดิมของ head
					id: headId,
				},
			};
		}

		if (approverId) {
			whereConditions = {
				...whereConditions,
				approver: {
					...(whereConditions.approver || {}), // รักษาเงื่อนไขเดิมของ approver
					id: approverId,
				},
			};
		}

		if (projectId) {
			whereConditions = {
				...whereConditions,
				project: {
					...(whereConditions.project || {}), // รักษาเงื่อนไขเดิมของ project
					id: projectId,
				},
			};
		}

		if (projectOwnerId) {
			whereConditions = {
				...whereConditions,
				project: {
					...(whereConditions.project || {}), // รักษาเงื่อนไขเดิมของ project
					employee: {
						...(whereConditions.project?.employee || {}), // รักษาเงื่อนไขเดิมของ project.employee
						id: projectOwnerId,
					},
				},
			};
		}

		if (departmentId.length > 0) {
			whereConditions = {
				...whereConditions,
				employee: {
					...(whereConditions.employee || {}), // รักษาเงื่อนไขเดิมของ employee
					department: {
						...(whereConditions.employee?.department || {}), // รักษาเงื่อนไขเดิมของ department
						id: In(departmentId),
					},
				},
			};
		}

		if (dateStart && dateEnd) {
			const dateStartFormat = Helper.formatDate(`${dateStart}`);
			const dateEndFormat = Helper.formatDate(`${dateEnd}`);

			whereConditions.date = Raw(
				alias => `${alias} BETWEEN :dateStart AND :dateEnd`,
				{ dateStart: dateStartFormat, dateEnd: dateEndFormat }
			);
		}

		const ots = await Ot.find({
			where: whereConditions,
			relations: {
				employee: true,
				approver: true,
				project: true
			},
			order: {
				code: 'ASC',
				date: 'ASC'
			}
		})

		for (const ot of ots) {

			let is_holiday = false
			let is_issue = true

			//check holiday
			const formattedDate = ot?.date
				? DateTime.fromSQL(ot.date.toString()).toLocal().toFormat('yyyy-MM-dd')
				: null;

			const getWorkShiftEmployee = await this.employeeService.getWorkShiftEmployee(employeeId, formattedDate)
			if (getWorkShiftEmployee) {
				const _is_holiday = getWorkShiftEmployee.is_holiday
				is_holiday = _is_holiday
			}
			//

			ot.isHoliday = is_holiday
		}

		const groupByEmployee = chain(ots)
			.groupBy((ot) => {
				if (!ot.approver) {
					return '*-*'
				}

				return ot?.approver?.code + '-' + ot?.approver?.fullname
			}).map((v, k) => {
				const code = k.split('-')


				const value = chain(v).groupBy((ot) => {
					return ot.employee.code + '-' + ot.employee.fullname
				}).map((v1, k1) => {

					const empCode = k1.split('-')


					return {
						code: empCode[0],
						employee: empCode[1],
						ots: v1,
						total: v1.reduce((sum, curr) => sum + curr.qtyHour, 0)
					}
				}).value()

				return ({
					code: code[0],
					name: code[1],
					ots: value,
				});
			}).value();
		console.log(groupByEmployee, 'groupByEmployee');

		return groupByEmployee
	}

	async exportOTRequest(body: any) {
		const groupByEmployee = await this.otRequestGroupByTeam(body)

		const data: any = []; // หรือ ots = data ที่รับมาจาก API
		// console.log(data);

		const workbook = new Workbook();
		const worksheet = workbook.addWorksheet('OT Request');
		// กำหนด Header
		worksheet.addRow([
			'REQUESTID', 'REQUESTDATE', 'STARTDATE', 'STARTTIME',
			'ENDTIME', 'TOTALHOURS', 'PROJECTCODE', 'PROJECTNAME', 'DETAIL', 'STATUS'
		]).eachCell((cell) => {
			cell.font = { bold: true };
			cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'D9D9D9' } };
			cell.border = {
				top: { style: 'thin' },
				bottom: { style: 'thin' },
				left: { style: 'thin' },
				right: { style: 'thin' },
			};
		});

		// เริ่มแถวแรกที่แถวที่ 2
		let rowIndex = 2;

		groupByEmployee.forEach((employee) => {
			// 1. แถวสำหรับ Employee ID (พื้นหลังสีฟ้า เต็มแถว)
			const employeeRow = worksheet.addRow([
				employee.code, '', '', '', '', '', '', '', '' // ค่าที่เหลือให้ว่าง
			]);

			employeeRow.eachCell((cell) => {
				cell.border = {
					top: { style: 'thin' },
					bottom: { style: 'thin' },
					left: { style: 'thin' },
					right: { style: 'thin' },
				};
				cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'CCECFF' } }; // สีฟ้า
				cell.font = { bold: true };
			});

			// Merge เซลล์ Employee ID เพื่อให้ชื่อพนักงานอยู่ในคอลัมน์เดียว
			worksheet.mergeCells(`A${employeeRow.number}:I${employeeRow.number}`);
			rowIndex++;

			employee.ots.forEach((otGroup) => {
				// 2. แถวสำหรับ Employee Name (พื้นหลังสีเหลือง)
				const empNameRow = worksheet.addRow([otGroup.code, otGroup.employee, '', '', '', '', '', '', '']);
				empNameRow.eachCell((cell) => {
					cell.border = {
						top: { style: 'thin' },
						bottom: { style: 'thin' },
						left: { style: 'thin' },
						right: { style: 'thin' },
					};
					cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF3CD' } };
					cell.font = { bold: true };
				});
				worksheet.mergeCells(`B${empNameRow.number}:I${empNameRow.number}`);
				rowIndex++;

				otGroup.ots.forEach((ot) => {

					// 3. แถวรายละเอียด OT (พื้นหลังสีชมพู)
					const otRow = worksheet.addRow([
						ot.code, ot.date, ot.date, ot.timeStart,
						ot.timeEnd, ot.qtyHour, ot.project?.code, ot.project?.name, ot.detail, this.checkStatus(ot.status)
					]);
					otRow.eachCell((cell) => {
						// ใส่เส้นขอบให้ทุกช่อง
						cell.border = {
							top: { style: 'thin' },
							bottom: { style: 'thin' },
							left: { style: 'thin' },
							right: { style: 'thin' },
						};

						// ตรวจสอบว่ามี isHoliday เป็น true หรือไม่
						if (ot.isHoliday) {
							cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'F8D7DA' } }; // สีชมพู
						}
					});
					rowIndex++;
				});

				// 4. แถว Total OT Hours
				const totalRow = worksheet.addRow(['', '', '', '', 'Total', otGroup.total, '', '', '']);

				// Merge เซลล์ 'Total' เพื่อให้ดูดีขึ้น
				// worksheet.mergeCells(`E${totalRow.number}:F${totalRow.number}`);

				totalRow.eachCell((cell) => {
					cell.font = { bold: true };
					cell.alignment = { horizontal: 'right' }; // จัดกลาง

					// ใส่เส้นขอบเฉพาะเซลล์ที่มีค่า
					cell.border = {
						top: { style: 'thin' },
						bottom: { style: 'thin' },
						left: { style: 'thin' },
						right: { style: 'thin' },
					};
				});

				rowIndex++;
			});
		});

		// กำหนดความกว้างของ Column
		worksheet.columns.forEach((column) => {
			column.width = 15;
		});
		// สร้างไฟล์ Excel
		// const buffer = await workbook.xlsx.writeBuffer();
		return workbook.xlsx.writeBuffer();
	}

	async exportOTRequestGroupByTeam(body: any) {
		const groupByEmployee = await this.otRequestGroupByTeam(body)

		const workbook = new Workbook();
		const worksheet = workbook.addWorksheet('OT Request Report Group By Team');
		// กำหนด Header
		worksheet.addRow([
			'REQUESTID', 'REQUESTDATE', 'STARTDATE', 'STARTTIME',
			'ENDTIME', 'TOTALHOURS', 'PROJECTCODE', 'PROJECTNAME', 'DETAIL'
		]).eachCell((cell) => {
			cell.font = { bold: true };
			cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'D9D9D9' } };
			cell.border = {
				top: { style: 'thin' },
				bottom: { style: 'thin' },
				left: { style: 'thin' },
				right: { style: 'thin' },
			};
		});

		// เริ่มแถวแรกที่แถวที่ 2
		let rowIndex = 2;

		groupByEmployee.forEach((employee) => {
			// 1. แถวสำหรับ Employee ID (พื้นหลังสีฟ้า เต็มแถว)
			const employeeRow = worksheet.addRow([
				employee.code, employee.name, '', '', '', '', '', '', '' // ค่าที่เหลือให้ว่าง
			]);

			employeeRow.eachCell((cell) => {
				cell.border = {
					top: { style: 'thin' },
					bottom: { style: 'thin' },
					left: { style: 'thin' },
					right: { style: 'thin' },
				};
				cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'CCECFF' } }; // สีฟ้า
				cell.font = { bold: true };
			});

			// Merge เซลล์ Employee ID เพื่อให้ชื่อพนักงานอยู่ในคอลัมน์เดียว
			worksheet.mergeCells(`A${employeeRow.number}:H${employeeRow.number}`);
			rowIndex++;

			employee.ots.forEach((otGroup) => {
				// 2. แถวสำหรับ Employee Name (พื้นหลังสีเหลือง)
				const empNameRow = worksheet.addRow([otGroup.code, otGroup.employee, '', '', '', '', '', '']);
				empNameRow.eachCell((cell) => {
					cell.border = {
						top: { style: 'thin' },
						bottom: { style: 'thin' },
						left: { style: 'thin' },
						right: { style: 'thin' },
					};
					cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF3CD' } };
					cell.font = { bold: true };
				});
				worksheet.mergeCells(`B${empNameRow.number}:H${empNameRow.number}`);
				rowIndex++;

				otGroup.ots.forEach((ot) => {
					// 3. แถวรายละเอียด OT (พื้นหลังสีชมพู)
					const otRow = worksheet.addRow([
						ot.code, ot.date, ot.date, ot.timeStart,
						ot.timeEnd, ot.qtyHour, ot.project?.code, ot.project?.name, ot.detail
					]);
					otRow.eachCell((cell) => {
						// ใส่เส้นขอบให้ทุกช่อง
						cell.border = {
							top: { style: 'thin' },
							bottom: { style: 'thin' },
							left: { style: 'thin' },
							right: { style: 'thin' },
						};

						// ตรวจสอบว่ามี isHoliday เป็น true หรือไม่
						if (ot.isHoliday) {
							cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'F8D7DA' } }; // สีชมพู
						}
					});
					rowIndex++;
				});

				// 4. แถว Total OT Hours
				const totalRow = worksheet.addRow(['', '', '', '', 'Total', otGroup.total, '', '']);

				// Merge เซลล์ 'Total' เพื่อให้ดูดีขึ้น
				// worksheet.mergeCells(`E${totalRow.number}:F${totalRow.number}`);

				totalRow.eachCell((cell) => {
					cell.font = { bold: true };
					cell.alignment = { horizontal: 'right' }; // จัดกลาง

					// ใส่เส้นขอบเฉพาะเซลล์ที่มีค่า
					cell.border = {
						top: { style: 'thin' },
						bottom: { style: 'thin' },
						left: { style: 'thin' },
						right: { style: 'thin' },
					};
				});

				rowIndex++;
			});
		});

		// กำหนดความกว้างของ Column
		worksheet.columns.forEach((column) => {
			column.width = 15;
		});
		// สร้างไฟล์ Excel
		// const buffer = await workbook.xlsx.writeBuffer();
		return workbook.xlsx.writeBuffer();
	}

	async exportOTRequestGroupByStaff(body: any) {
		const data = await this.otComparisonReport(body)

		const workbook = new Workbook();
		const worksheet = workbook.addWorksheet('OT Request Report Group By Staff');
		// กำหนด Header
		worksheet.addRow([
			'REQUESTID',
			'DATE',
			'STARTTIME',
			'ENDTIME',
			'TOTALHOURS',
			'PROJECTCODE',
			'PROJECTNAME',
			'DETAIL',
			'APPROVER',
			'TIMEIN',
			'TIMEOUT',
		]).eachCell((cell) => {
			cell.font = { bold: true };
			cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'D9D9D9' } };
			cell.border = {
				top: { style: 'thin' },
				bottom: { style: 'thin' },
				left: { style: 'thin' },
				right: { style: 'thin' },
			};
		});

		// เริ่มแถวแรกที่แถวที่ 2
		let rowIndex = 2;

		data.sort((a, b) => a.name.localeCompare(b.name)).forEach((employee) => {
			// 1. แถวสำหรับ Employee ID (พื้นหลังสีฟ้า เต็มแถว)
			const employeeRow = worksheet.addRow([
				employee.code, employee.name, '', '', '', '', '', '', '', '' // ค่าที่เหลือให้ว่าง
			]);

			employeeRow.eachCell((cell) => {
				cell.border = {
					top: { style: 'thin' },
					bottom: { style: 'thin' },
					left: { style: 'thin' },
					right: { style: 'thin' },
				};
				// cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'CCECFF' } }; // สีฟ้า
				cell.font = { bold: true };
			});

			// Merge เซลล์ Employee ID เพื่อให้ชื่อพนักงานอยู่ในคอลัมน์เดียว
			worksheet.mergeCells(`b${employeeRow.number}:J${employeeRow.number}`);
			rowIndex++;

			employee.ots
  .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
  .forEach((otGroup) => {
    const otRow = worksheet.addRow([
      otGroup.code,
      otGroup.date,
      otGroup.timeStart,
      otGroup.timeEnd,
      otGroup.qtyHour,
      otGroup.project?.code,
      otGroup.project?.name,
      otGroup.detail,
      otGroup.approver?.firstname,
      otGroup['in'],
      otGroup['out'],
    ]);

    otRow.eachCell((cell) => {
      cell.border = {
        top: { style: 'thin' },
        bottom: { style: 'thin' },
        left: { style: 'thin' },
        right: { style: 'thin' },
      };

      if (otGroup.isHoliday) {
        cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'F8D7DA' } };
      }
    });

    rowIndex++;
  });
			const totalRow = worksheet.addRow(['', '', '', 'Total', employee.total, '', '', '', '', '']);
			// Merge เซลล์ 'Total' เพื่อให้ดูดีขึ้น
			// worksheet.mergeCells(`E${totalRow.number}:F${totalRow.number}`);

			totalRow.eachCell((cell) => {
				cell.font = { bold: true };
				cell.alignment = { horizontal: 'right' }; // จัดกลาง

				// ใส่เส้นขอบเฉพาะเซลล์ที่มีค่า
				cell.border = {
					top: { style: 'thin' },
					bottom: { style: 'thin' },
					left: { style: 'thin' },
					right: { style: 'thin' },
				};
			});
		});

		// กำหนดความกว้างของ Column
		worksheet.columns.forEach((column) => {
			column.width = 15;
		});
		// สร้างไฟล์ Excel
		// const buffer = await workbook.xlsx.writeBuffer();
		return workbook.xlsx.writeBuffer();
	}

	async exportOTRequestByProjectOwner(body: any) {
		const data = await this.otRequestReport(body)

		const workbook = new Workbook();
		const worksheet = workbook.addWorksheet('OT Request Report By Project Owner');
		// กำหนด Header
		worksheet.addRow([
			'REQUESTID',
			'FULLNAME',
			'REQUESTDATE',
			'OT DATE',
			'STARTTIME',
			'ENDTIME',
			'HOURS',
			'PROJECT',
			'DETAIL',
			'REVIEWER',
			'APPROVER',
			'STATUS',
		]).eachCell((cell) => {
			cell.font = { bold: true };
			cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'D9D9D9' } };
			cell.border = {
				top: { style: 'thin' },
				bottom: { style: 'thin' },
				left: { style: 'thin' },
				right: { style: 'thin' },
			};
		});

		// เริ่มแถวแรกที่แถวที่ 2
		let rowIndex = 2;

		let totalQtyHour = 0;

		data.sort((a, b) => {
			const nameA = `${a.employee?.firstname || ''} ${a.employee?.lastname || ''}`.toLowerCase();
			const nameB = `${b.employee?.firstname || ''} ${b.employee?.lastname || ''}`.toLowerCase();
			return nameA.localeCompare(nameB);
		});

		data.forEach((employee) => {
			const {
				code, createdAt, date, timeStart, timeEnd, qtyHour, detail, status, isHoliday,
				employee: emp, project, head, approver
			} = employee;

			totalQtyHour += qtyHour || 0;

			const employeeRow = worksheet.addRow([
				code,
				`${emp?.firstname || ''} ${emp?.lastname || ''}`,
				createdAt,
				date,
				timeStart,
				timeEnd,
				qtyHour,
				project?.code || '',
				detail,
				`${head?.firstname || ''} ${head?.lastname || ''}`,
				`${approver?.firstname || ''} ${approver?.lastname || ''}`,
				this.checkStatus(status),
			]);

			// กำหนดสไตล์ของ Cell
			employeeRow.eachCell((cell) => {
				cell.border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };
				if (isHoliday) {
					cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'F8D7DA' } }; // สีชมพู
				}
				cell.font = { bold: true };

			});
		});

		// เพิ่มแถวสรุปที่ด้านล่าง
		const summaryRow = worksheet.addRow([
			'', '', '', '', '', 'Total', totalQtyHour, '', '', '', '', ''
		]);

		summaryRow.eachCell((cell, colNumber) => {
			cell.font = { bold: true };
			cell.border = {
				top: { style: 'thin' },
				bottom: { style: 'thin' },
				left: { style: 'thin' },
				right: { style: 'thin' },
			};
			cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF3CD' } };
		});

		// กำหนดความกว้างของ Column
		worksheet.columns.forEach((column) => {
			column.width = 15;
		});
		// สร้างไฟล์ Excel
		return workbook.xlsx.writeBuffer();
	}


	async exportViewPendingOTRequest(body: any) {
		const data = await this.otRequestReport(body)

		const workbook = new Workbook();
		const worksheet = workbook.addWorksheet('View Pending Ot Request');
		// กำหนด Header
		worksheet.addRow([
			'REQUESTID',
			'FULLNAME',
			'REQUESTDATE',
			'OT DATE',
			'STARTTIME',
			'ENDTIME',
			'HOURS',
			'REVIEWER',
			'APPROVER',
			'STATUS',
		]).eachCell((cell) => {
			cell.font = { bold: true };
			cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'D9D9D9' } };
			cell.border = {
				top: { style: 'thin' },
				bottom: { style: 'thin' },
				left: { style: 'thin' },
				right: { style: 'thin' },
			};
		});

		// เริ่มแถวแรกที่แถวที่ 2
		let rowIndex = 2;
		console.log('data', data);

		let totalQtyHour = 0;

		// กรองเฉพาะรายการที่ status เป็น "open" และ head_approved เป็น true
		const filteredData = data.filter(employee => employee.status === "open" || employee.status === "head_approved");

		filteredData.sort((a, b) => {
			const nameA = `${a.employee?.firstname || ''} ${a.employee?.lastname || ''}`.toLowerCase();
			const nameB = `${b.employee?.firstname || ''} ${b.employee?.lastname || ''}`.toLowerCase();
			return nameA.localeCompare(nameB);
		});

		filteredData.forEach((employee) => {
			const {
				code,
				createdAt,
				date,
				timeStart,
				timeEnd,
				qtyHour,
				status,
				isHoliday,
				employee: emp, project, head, approver
			} = employee;

			totalQtyHour += qtyHour || 0;

			const employeeRow = worksheet.addRow([
				code,
				`${emp?.firstname || ''} ${emp?.lastname || ''}`,
				createdAt,
				date,
				timeStart,
				timeEnd,
				qtyHour,
				`${head?.firstname || ''} ${head?.lastname || ''}`,
				`${approver?.firstname || ''} ${approver?.lastname || ''}`,
				this.checkStatus(status),
			]);

			// กำหนดสไตล์ของ Cell
			employeeRow.eachCell((cell) => {
				cell.border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };
				if (isHoliday) {
					cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'F8D7DA' } }; // สีชมพู
				}
				cell.font = { bold: true };
			});
		});

		// กำหนดความกว้างของ Column
		worksheet.columns.forEach((column) => {
			column.width = 15;
		});
		// สร้างไฟล์ Excel
		return workbook.xlsx.writeBuffer();
	}

	async exportTimeAttendanceViewByUser(body) {
		let _year = body.year
		let _month = body.month
		let _employeeId = body.employeeId
		const data = await this.timeAttendanceViewByUser(_year, _month, _employeeId);
		const filterData = data.data;
		const workbook = new Workbook();
		const worksheet = workbook.addWorksheet('Time Attendance View BY user');

		const labelsArray = data.fields.map(item => item.label);

		// กำหนด Header
		const headerRow = worksheet.addRow(labelsArray);
		headerRow.eachCell((cell) => {
			cell.font = { bold: true };
			cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'D9D9D9' } };
			cell.border = {
				top: { style: 'thin' },
				bottom: { style: 'thin' },
				left: { style: 'thin' },
				right: { style: 'thin' },
			};
		});

		// เริ่มแถวแรกที่แถวที่ 2
		filterData.forEach((employee) => {
			// ใช้ค่าที่ไม่เป็น null หรือ undefined ถ้าเป็น null หรือ undefined ให้ใช้ ''
			const employeeRow = worksheet.addRow([
				employee.date ?? '',
				employee.day ?? '',
				employee.timeIn ?? '',
				employee.timeOut ?? '',
				employee.calcHrs ?? '',
				employee.workHrs ?? '',
				employee.Ann ?? '',
				employee.Cas ?? '',
				employee.Sic ?? '',
				employee.Wit ?? '',
				employee.Oth ?? '',
				employee['In '] ?? '',
				employee.Mat ?? '',
				employee.Car ?? '',
			]);

			// นำ # ออกจากค่า color (ถ้ามี) และกำหนดสีพื้นหลังของแถว
			const rowColor = employee.color ? employee.color.replace('#', '') : 'FFCC80';

			employeeRow.eachCell((cell) => {
				cell.border = {
					top: { style: 'thin' },
					bottom: { style: 'thin' },
					left: { style: 'thin' },
					right: { style: 'thin' },
				};
				cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: rowColor } };
				cell.font = { bold: true };
			});
		});

		// กำหนดความกว้างของ Column
		worksheet.columns.forEach((column) => {
			column.width = 15;
		});

		// สร้างไฟล์ Excel
		return workbook.xlsx.writeBuffer();
	}

	async exportDailyAttendanceReport(body: any) {
		const data = await this.dailyAttendanceReport(body)

		const workbook = new Workbook();
		const worksheet = workbook.addWorksheet('Daily Attendance Report');
		// กำหนด Header
		worksheet.addRow([
			'DATE',
			'USER ID',
			'USER NAME',
			'TIME IN',
			'TIME OUT',
			'CALC. HRS',

		]).eachCell((cell) => {
			cell.font = { bold: true };
			cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'D9D9D9' } };
			cell.border = {
				top: { style: 'thin' },
				bottom: { style: 'thin' },
				left: { style: 'thin' },
				right: { style: 'thin' },
			};
		});

		// เริ่มแถวแรกที่แถวที่ 2
		let rowIndex = 2;
		console.log('data', data);

		data.forEach((employee) => {

			const employeeRow = worksheet.addRow([
				employee.date,
				employee.code,
				`${employee?.firstname || ''} ${employee?.lastname || ''}`,
				employee.min,
				employee.max,
				employee.calhrs,
			]);
			// กำหนดสไตล์ของ Cell
			employeeRow.eachCell((cell) => {
				cell.border = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } };
				cell.font = { bold: true };
			});
		});

		// กำหนดความกว้างของ Column
		worksheet.columns.forEach((column) => {
			column.width = 15;
		});
		// สร้างไฟล์ Excel
		return workbook.xlsx.writeBuffer();
	}

	async exportMonthlyAttendanceReport(body: any) {
		const data = await this.monthlyAttendanceReport(body)

		const workbook = new Workbook();
		const worksheet = workbook.addWorksheet('Month Attendance Report');
		// กำหนด Header
		worksheet.addRow([
			'DATE',
			'TIME IN',
			'TIME OUT',
			'CALC.HRS',
			'WORK HRS',
			'CASE 1',
			'CASE 2',
			'CASE 3',
			'CASE 4',
			'CASUAL',
			'WITHOUT PAY',
			'IN LIEU',
			'SICK',
			'OTHERS',
			'CARRY-OVER',
			'MATERNITY',
			'ANNUAL',
			'DEDUCT',
			'EXPLANAION',
			'TEAM LEAD EXPLANATION',
		]).eachCell((cell) => {
			cell.font = { bold: true };
			cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'D9D9D9' } };
			cell.border = {
				top: { style: 'thin' },
				bottom: { style: 'thin' },
				left: { style: 'thin' },
				right: { style: 'thin' },
			};
		});

		// เริ่มแถวแรกที่แถวที่ 2
		let rowIndex = 2;
		let previousEmployeeCode = '';

		data.sort((a, b) => {
			const nameA = `${a.employee?.firstname || ''} ${a.employee?.lastname || ''}`.toLowerCase();
			const nameB = `${b.employee?.firstname || ''} ${b.employee?.lastname || ''}`.toLowerCase();
			return nameA.localeCompare(nameB);
		});

		data.forEach((employee) => {
			const employeeCode = employee.employee?.code || '';
			const employeeName = `${employee.employee?.firstname || ''} ${employee.employee?.lastname || ''}`;

			// ✅ หาก Employee Code เปลี่ยน ให้เพิ่มชื่อพนักงาน
			if (employeeCode !== previousEmployeeCode) {
				// นำ # ออกจาก color (ถ้ามี) หรือใช้สีเริ่มต้น
				const headRow = worksheet.addRow([`${employeeCode} ${employeeName}`]);
				// Merge Cell ตั้งแต่คอลัมน์แรกถึงคอลัมน์สุดท้ายของตาราง
				worksheet.mergeCells(`A${headRow.number}:T${headRow.number}`);
				// กำหนดสไตล์ของแถวหัวข้อพนักงาน
				headRow.eachCell((cell) => {
					cell.font = { bold: true };
					cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF3CD' } };
					cell.border = {
						top: { style: 'thin' },
						bottom: { style: 'thin' },
						left: { style: 'thin' },
						right: { style: 'thin' },
					};
				});

				previousEmployeeCode = employeeCode;
				// worksheet.mergeCells(`A${employeeName}:T${employeeName}`);
			}



			const employeeRow = worksheet.addRow([
				employee.workDate ? new Date(employee.workDate).toLocaleDateString('en-GB') : '',
				employee.checkIn || '',
				employee.checkOut || '',
				employee.calc_hrs || 0,
				employee.work_hrs || 0,
				employee.case1 || 0,
				employee.case2 || 0,
				employee.case3 || 0,
				employee.case4 || 0,
				...employee.column_leave_type.map((leave) => leave.qty || 0),
				employee.deduct ? 'Yes' : 'No',
				employee.reason || '',
				employee.explanation || '',
			]);

			employeeRow.eachCell((cell) => {
				cell.border = {
					top: { style: 'thin' },
					bottom: { style: 'thin' },
					left: { style: 'thin' },
					right: { style: 'thin' },
				};

				cell.font = { bold: true };
			});
			rowIndex++;



		});

		// กำหนดความกว้างของ Column
		worksheet.columns.forEach((column) => {
			column.width = 15;
		});
		// สร้างไฟล์ Excel
		// const buffer = await workbook.xlsx.writeBuffer();
		return workbook.xlsx.writeBuffer();
	}

	async exportSummaryDeductReport(body: any) {
		const data = await this.summaryDeductionReport(body)
		console.log(data, '1');

		const workbook = new Workbook();
		const worksheet = workbook.addWorksheet('Summary Deduction Report');
		// กำหนด Header
		worksheet.addRow([
			'USER CODE',
			'USER NAME',
			'CASE 1',
			'CASE 2',
			'CASE 3',
			'CASE 4',
			'DEDUCTION HRS',
		]).eachCell((cell) => {
			cell.font = { bold: true };
			cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'D9D9D9' } };
			cell.border = {
				top: { style: 'thin' },
				bottom: { style: 'thin' },
				left: { style: 'thin' },
				right: { style: 'thin' },
			};
		});

		// เริ่มแถวแรกที่แถวที่ 2
		let rowIndex = 2;

		data.forEach((employee) => {
			if (employee.employee.length > 0 && employee.employee.some(staff => staff.total_deduct_min > 0)) { // ตรวจสอบว่ามีข้อมูลพนักงานหรือไม่
				// 1. แถวสำหรับ Employee ID (พื้นหลังสีฟ้า เต็มแถว)
				const employeeRow = worksheet.addRow([
					employee.name, '', '', '', '', '', '', // ค่าที่เหลือให้ว่าง
				]);
				employeeRow.eachCell((cell) => {
					cell.border = {
						top: { style: 'thin' },
						bottom: { style: 'thin' },
						left: { style: 'thin' },
						right: { style: 'thin' },
					};
					cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'CCECFF' } }; // สีฟ้า
					cell.font = { bold: true };
				});
				// Merge เซลล์ Employee ID เพื่อให้ชื่อพนักงานอยู่ในคอลัมน์เดียว
				worksheet.mergeCells(`A${employeeRow.number}:G${employeeRow.number}`);
				rowIndex++;

				employee.employee.forEach((deduct) => {
					if (deduct.total_deduct_min > 0) {
						// 2. แถวสำหรับ Employee Name (พื้นหลังสีเหลือง)
						const empNameRow = worksheet.addRow([
							deduct.code,
							`${deduct?.firstname || ''} ${deduct?.lastname || ''}`,
							deduct.total_case1,
							deduct.total_case2,
							deduct.total_case3,
							deduct.total_case4,
							deduct.total_deduct_min,

						]);
						empNameRow.eachCell((cell) => {
							cell.border = {
								top: { style: 'thin' },
								bottom: { style: 'thin' },
								left: { style: 'thin' },
								right: { style: 'thin' },
							};
							cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF3CD' } };
							cell.font = { bold: true };
						});
						rowIndex++;
					}
				});
			}
		});

		// กำหนดความกว้างของ Column
		worksheet.columns.forEach((column) => {
			column.width = 15;
		});
		// สร้างไฟล์ Excel
		// const buffer = await workbook.xlsx.writeBuffer();
		return workbook.xlsx.writeBuffer();
	}

	async detailAccumulateStaffReportExcel(body: any) {

		const data: any[] = await this.detailAccumulateStaff(body)

		const workbook = new Workbook();
		const worksheet = workbook.addWorksheet('Detail Accumulate Staff');

		worksheet.addRow([
			'NAME',
			'FROM',
			'TO',
			'LEAVE TYPE',
			'LEAVE TOTAL',
			'ENTITLEMENT',
			'TAKEN',
			'REMAIN',
			'ECESS',
			'STATUS',
		]).eachCell((cell) => {
			cell.font = { bold: true };
			cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'D9D9D9' } };
			cell.border = {
				top: { style: 'thin' },
				bottom: { style: 'thin' },
				left: { style: 'thin' },
				right: { style: 'thin' },
			};
		});

		let rowIndex = 2;
		data.forEach((employee) => {

			// 1. แถวสำหรับ Employee ID (พื้นหลังสีฟ้า เต็มแถว)
			const employeeRow = worksheet.addRow([
				`${employee?.firstname || ''} ${employee?.lastname || ''}`, '', '', '', '', '', '', '', '', '', // ค่าที่เหลือให้ว่าง
			]);
			employeeRow.eachCell((cell) => {
				cell.border = {
					top: { style: 'thin' },
					bottom: { style: 'thin' },
					left: { style: 'thin' },
					right: { style: 'thin' },
				};
				cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FEF3C7' } }; // สีเหลือง
				cell.font = { bold: true };
			});
			// Merge เซลล์ Employee ID เพื่อให้ชื่อพนักงานอยู่ในคอลัมน์เดียว
			worksheet.mergeCells(`A${employeeRow.number}:J${employeeRow.number}`);
			rowIndex++;

			employee.leaves.forEach((leave) => {
				// 2. แถวสำหรับ Employee Name (พื้นหลังสีเหลือง)
				const empNameRow = worksheet.addRow([
					'',
					leave.dateStart,
					leave.dateEnd,
					leave.leaveType.name,
					leave.qtyDay,
					leave.entitlementDay,
					leave.takenDay,
					leave.remainDay,
					leave.excessDay,
					this.checkStatusLeave(leave.status),

				]);
				empNameRow.eachCell((cell) => {
					cell.border = {
						top: { style: 'thin' },
						bottom: { style: 'thin' },
						left: { style: 'thin' },
						right: { style: 'thin' },
					};
					// cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF3CD' } };
					cell.font = { bold: true };
				});
				rowIndex++;

			});

		});

		// กำหนดความกว้างของ Column
		worksheet.columns.forEach((column) => {
			column.width = 15;
		});
		// Write data to buffer and return
		return workbook.xlsx.writeBuffer();
	}

	async AccumulateStaffReportExcel(body: any) {

		const data: any[] = await this.reportAccumulateStaff(body)

		const workbook = new Workbook();
		const worksheet = workbook.addWorksheet('Accumulate Staff');

		worksheet.addRow([
			'DEPARTMENT',
			'NAME',
			'LEAVE',
			'ENTITLEMENT',
			'TAKEN',
			'REMAIN',
			'EXCESS',
		]).eachCell((cell) => {
			cell.font = { bold: true };
			cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'D9D9D9' } };
			cell.border = {
				top: { style: 'thin' },
				bottom: { style: 'thin' },
				left: { style: 'thin' },
				right: { style: 'thin' },
			};
		});

		let rowIndex = 2;
		data.forEach((department) => {

			// 1. แถวสำหรับ Employee ID (พื้นหลังสีฟ้า เต็มแถว)
			const departmentRow = worksheet.addRow([
				`${department?.name || ''} `, '', '', '', '', '', '', // ค่าที่เหลือให้ว่าง
			]);
			departmentRow.eachCell((cell) => {
				cell.border = {
					top: { style: 'thin' },
					bottom: { style: 'thin' },
					left: { style: 'thin' },
					right: { style: 'thin' },
				};
				cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'CCECFF' } }; // สีเหลือง
				cell.font = { bold: true };
			});
			// Merge เซลล์ Employee ID เพื่อให้ชื่อพนักงานอยู่ในคอลัมน์เดียว
			worksheet.mergeCells(`A${departmentRow.number}:G${departmentRow.number}`);
			rowIndex++;

			department.employee.forEach((employee) => {
				// 2. แถวสำหรับ Employee Name (พื้นหลังสีเหลือง)
				const employeeRow = worksheet.addRow([
					'', `${employee?.firstname || ''} ${employee?.lastname || ''}`, '', '', '', '',
				]);
				employeeRow.eachCell((cell) => {
					cell.border = {
						top: { style: 'thin' },
						bottom: { style: 'thin' },
						left: { style: 'thin' },
						right: { style: 'thin' },
					};
					cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFF3CD' } };
					cell.font = { bold: true };
				});
				worksheet.mergeCells(`B${employeeRow.number}:G${employeeRow.number}`);
				rowIndex++;

				employee.employeeLeavePermissions.forEach((leave) => {
					// 2. แถวสำหรับ Employee Name (พื้นหลังสีเหลือง)
					const employeeRow = worksheet.addRow([
						'', '',
						leave?.leaveType?.name || 0,
						leave?.leavePermission?.Entitlement || 0,
						leave?.leavePermission?.Taken || 0,
						leave?.leavePermission?.Remain || 0,
						leave?.leavePermission?.Excess || 0,
					]);
					employeeRow.eachCell((cell) => {
						cell.border = {
							top: { style: 'thin' },
							bottom: { style: 'thin' },
							left: { style: 'thin' },
							right: { style: 'thin' },
						};
						cell.font = { bold: true };
					});
					rowIndex++;
				});
			});

		});

		// กำหนดความกว้างของ Column
		worksheet.columns.forEach((column) => {
			column.width = 15;
		});
		// Write data to buffer and return
		return workbook.xlsx.writeBuffer();
	}


	checkStatus(status: string | null | undefined): string {
		if (!status) {
			return '-';
		}
		switch (status) {
			case 'open':
				return 'Request';
			case 'head_approved':
				return 'Reviewed';
			case 'approved':
				return 'Approved';
			case 'cancel':
				return 'Canceled';
			case 'reject':
				return 'Rejected';
			default:
				return '-';
		}
	}

	checkStatusLeave(status: string | null | undefined): string {
		if (!status) {
			return '-'; // คืนค่าเริ่มต้นถ้า status เป็น null หรือ undefined
		}
		switch (status) {
			case 'open':
				return 'On process';
			case 'reject':
				return 'Rejected';
			case 'approved':
				return 'Approved';
			case 'cancel':
				return 'Canceled';
			default:
				return '-'; // คืนค่าเริ่มต้นหาก status ไม่ตรงกับกรณีที่กำหนด
		}
	}
}

