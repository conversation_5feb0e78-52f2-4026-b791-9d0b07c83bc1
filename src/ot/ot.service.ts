
import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { CreateOtDto } from './dto/create-ot.dto';
import { UpdateOtDto } from './dto/update-ot.dto';
import { UpdateStatusOtDto } from './dto/update-status-ot.dto';
import { DataSource, Repository, Raw, Not } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { FilterOperator, PaginateConfig, PaginateQuery, Paginated, paginate } from 'nestjs-paginate';
import { Helper } from 'src/common/utils/helper';

import { Employee } from 'src/employee/entities/employee.entity';

import { MailerService } from '@nestjs-modules/mailer';
import { DateTime } from 'luxon';
import { Ot, OtStatus } from './entities/ot.entity';
import { Project } from 'src/project/entities/project.entity';
import { EmployeeService } from 'src/employee/employee.service';
import { User } from 'src/user/entities/user.entity';

export const OT_PAGINATION_CONFIG: PaginateConfig<Ot> = {
  relations: ['employee', 'project', 'head', 'approver'],
  sortableColumns: [
    'id',
    'code',
    'date',
    'timeStart',
    'timeEnd',
    'qtyHour',
    'detail',
    'status',
    'active',
    'employee.id',
    'employee.code',
    'employee.firstname',
    'employee.lastname',
    'project.id',
    'project.code',
    'project.name',
    'head.id',
    'head.code',
    'head.firstname',
    'head.lastname',
    'approver.id',
    'approver.code',
    'approver.firstname',
    'approver.lastname',
    'status',
    'statusRemark',
    'statusDate'
  ],
  select: [
    'id',
    'code',
    'date',
    'timeStart',
    'timeEnd',
    'qtyHour',
    'detail',
    'status',
    'active',
    'employee.id',
    'employee.code',
    'employee.firstname',
    'employee.lastname',
    'project.id',
    'project.code',
    'project.name',
    'head.id',
    'head.code',
    'head.firstname',
    'head.lastname',
    'approver.id',
    'approver.code',
    'approver.firstname',
    'approver.lastname',
    'status',
    'statusRemark',
    'statusDate'
  ],
  searchableColumns: ['code', 'employee.code', 'employee.firstname', 'employee.lastname'],
  filterableColumns: {
    code: [FilterOperator.EQ],
    date: [FilterOperator.EQ, FilterOperator.GTE, FilterOperator.LTE, FilterOperator.BTW],
    timeStart: [FilterOperator.EQ, FilterOperator.GTE, FilterOperator.LTE],
    timeEnd: [FilterOperator.EQ, FilterOperator.GTE, FilterOperator.LTE],
    status: [FilterOperator.IN],
    'employee.id': [FilterOperator.EQ],
    'project.id': [FilterOperator.EQ],
    'head.id': [FilterOperator.EQ],
    'approver.id': [FilterOperator.EQ]
  },
};
@Injectable()
export class OtService {
  constructor(
    @InjectRepository(Ot)
    private otRepository: Repository<Ot>,

    @InjectRepository(Project)
    private projectRepository: Repository<Project>,

    @InjectRepository(Employee)
    private employeeRepository: Repository<Employee>,

    private readonly employeeService: EmployeeService,

    private dataSource: DataSource,
    private readonly mailerService: MailerService,

  ) { }

  async datatables(query: PaginateQuery): Promise<Paginated<Ot>> {
    return paginate(query, this.otRepository, OT_PAGINATION_CONFIG);
  }

  async create(createOtDto: CreateOtDto) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {

      const { projectId, headId, employeeId, date, timeStart, timeEnd } = createOtDto;

      const [checkProject, employee, head] = await Promise.all([
        this.projectRepository.findOne({ where: { id: projectId } }),
        this.employeeRepository.findOne({ where: { id: employeeId }, relations: { employeeType: true } }),
        this.employeeRepository.findOne({ relations: ['head'], where: { id: headId } })
      ]);

      if (!checkProject) throw new NotFoundException("project not found");
      if (!employee) throw new NotFoundException("employee not found");
      if (!head) throw new NotFoundException("head not found");

      //get last id
      const lastOt = await Ot.find({
        where: { active: true },
        order: { id: 'DESC' }
      });

      const lastId = lastOt.length;

      //add ot
      const otCode = Helper.generateNo('OT', lastId).toString();
      // คำนวณ
      let qtyHour = Helper.calculateHours(createOtDto.timeStart, createOtDto.timeEnd);

      //check duplicate ot
      const Date = DateTime.fromSQL(date).toLocal().toJSDate();

      const check = await Ot.findOne({
        where: [
          {
            date: Date,
            status: Not(OtStatus.cancel),
            employee: {
              id: employeeId
            },
            timeStart: Raw(alias => `${alias} >= :timeStart AND ${alias} <= :timeEnd`, {
              timeStart: timeStart,
              timeEnd: timeEnd,
            }),
          },
          {
            date: Date,
            status: Not(OtStatus.cancel),
            employee: {
              id: employeeId
            },
            timeEnd: Raw(alias => `${alias} >= :timeStart AND ${alias} <= :timeEnd`, {
              timeStart: timeStart,
              timeEnd: timeEnd,
            }),
          },
        ],
      });

      if (check) {
        throw new NotFoundException("Unable to request OT because OT has already been requested during this time.");
      }
      //  

      const newOt = Ot.create({
        code: otCode,
        date: createOtDto.date,
        timeStart: createOtDto.timeStart,
        timeEnd: createOtDto.timeEnd,
        detail: createOtDto.detail,
        qtyHour: qtyHour,
        status: OtStatus.open,
        project: { id: projectId },
        employee: { id: employeeId },
        head: { id: headId },
        approver: { id: head?.head?.id },
      });

      // บันทึก ot
      await queryRunner.manager.save(Ot, newOt)


      await queryRunner.commitTransaction();

      //sent email to Approver head
      this.mailerService.sendMail({
        to: head.email,
        subject: 'Overtime (OT) Request',
        template: 'ot-approve',
        context: {
          fullname: employee.fullname,
          project: checkProject.name,
          date: DateTime.fromISO(date).toLocal().toFormat('dd/MM/yyyy'),
          timeStart: DateTime.fromISO(timeStart).toLocal().toFormat('HH:mm'),
          timeEnd: DateTime.fromISO(timeEnd).toLocal().toFormat('HH:mm'),
          total: qtyHour,
          detail: newOt?.detail,
          // web: process.env.WEB_URL + '/ot/ot-review',
          web: process.env.WEB_URL + '/sign-in',
        }
      })

      //senf email to Requester
      this.mailerService.sendMail({
        to: employee.email,
        subject: 'Overtime (OT) Request',
        template: 'ot-request',
        context: {
          fullname: employee.fullname,
          project: checkProject.name,
          date: DateTime.fromISO(date).toLocal().toFormat('dd/MM/yyyy'),
          timeStart: DateTime.fromISO(timeStart).toLocal().toFormat('HH:mm'),
          timeEnd: DateTime.fromISO(timeEnd).toLocal().toFormat('HH:mm'),
          total: qtyHour,
          detail: newOt?.detail,
          web: '',
        }
      })

      return newOt;

    } catch (err) {
      await queryRunner.rollbackTransaction();
      console.error(err);
      throw new BadRequestException(err?.message)
    } finally {
      await queryRunner.release();
    }
  }

  findAll(query) {
    const projectId = query.projectId;
    const employeeId = query.employeeId;
    const headId = query.headId;
    const approverId = query.approver_id;
    const year = query.year;
    const month = query.month;

    // คำนวณช่วงเวลาเริ่มต้นและสิ้นสุดเดือน
    const startDate = year && month
      ? DateTime.fromObject({ year: Number(year), month: Number(month), day: 1 }).toISODate()
      : null;

    const endDate = startDate
      ? DateTime.fromObject({ year: Number(year), month: Number(month), day: 1 })
        .endOf('month')
        .toISODate()
      : null;

    const yearMonthFilter = startDate && endDate
      ? {
        date: Raw(alias => `${alias} BETWEEN :startDate AND :endDate`, {
          startDate,
          endDate,
        }),
      }
      : {};

    return this.otRepository.find({
      relations: ['employee', 'project', 'head', 'approver'],
      where: {
        ...yearMonthFilter,
        project: projectId ? { id: projectId } : null,
        employee: employeeId ? { id: employeeId } : null,
        head: headId ? { id: headId } : null,
        approver: approverId ? { id: approverId } : null,
      },
    });
  }



  async findOne(id: number) {
    const item = await this.otRepository.findOne({
      relations: ['employee', 'project', 'head', 'approver'],
      where: { id }
    });

    if (!item) throw new NotFoundException("ot request not found");

    return item;
  }

  async update(id: number, updateOtDto: UpdateOtDto) {

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {

      const item = await this.findOneById(id);
      if (!item) throw new NotFoundException("employee not found");

      const { projectId, headId, employeeId, date, timeStart, timeEnd } = updateOtDto;

      const [checkProject, employee, head] = await Promise.all([
        this.projectRepository.findOne({ where: { id: projectId } }),
        this.employeeRepository.findOne({ where: { id: employeeId }, relations: { employeeType: true } }),
        this.employeeRepository.findOne({ relations: ['head'], where: { id: headId } })
      ]);

      if (!checkProject) throw new NotFoundException("project not found");
      if (!employee) throw new NotFoundException("employee not found");
      if (!head) throw new NotFoundException("head not found");

      // คำนวณ
      let qtyHour = Helper.calculateHours(updateOtDto.timeStart, updateOtDto.timeEnd);

      //check duplicate ot
      const Date = DateTime.fromSQL(date).toLocal().toJSDate();

      const checkOt = await this.otRepository.findOne({
        relations: ['employee', 'project', 'head', 'approver'],
        where: { id }
      });

      const check = await Ot.findOne({
        where: [
          {
            id: Not(checkOt.id),
            date: Date,
            status: Not(OtStatus.cancel),
            employee: {
              id: employeeId
            },
            timeStart: Raw(alias => `${alias} >= :timeStart AND ${alias} <= :timeEnd`, {
              timeStart: timeStart,
              timeEnd: timeEnd,
            }),
          },
          {
            id: Not(checkOt.id),
            date: Date,
            status: Not(OtStatus.cancel),
            employee: {
              id: employeeId
            },
            timeEnd: Raw(alias => `${alias} >= :timeStart AND ${alias} <= :timeEnd`, {
              timeStart: timeStart,
              timeEnd: timeEnd,
            }),
          },
        ],
      });
      console.log(checkOt.id);
      console.log(check);


      if (check) {
        throw new NotFoundException("Unable to request OT because OT has already been requested during this time.");
      }
      //

      // อัปเดตข้อมูล ot
      await this.otRepository.update(id, {
        date: updateOtDto.date,
        timeStart: updateOtDto.timeStart,
        timeEnd: updateOtDto.timeEnd,
        detail: updateOtDto.detail,
        qtyHour: qtyHour,
        // status: 'open',
        project: { id: projectId },
        employee: { id: employeeId },
        head: { id: headId },
        approver: { id: head?.head?.id },
      });

      await queryRunner.commitTransaction();

      return item;

    } catch (error) {
      await queryRunner.rollbackTransaction();
      return error.response
    } finally {
      await queryRunner.release();
    }
  }


  async remove(id: number) {
    const item = await this.findOneById(id);
    if (!item) throw new NotFoundException("employee not found");

    item.code = `${Date.now()}-${item.code}`
    await item.save()

    await this.otRepository.softDelete(id);
  }

  findOneById(id: number) {
    return this.otRepository.findOne({
      relations: ['employee', 'project', 'head', 'approver'],
      where: { id }
    });
  }


  async updateStatus(id: number, updateStatusOtDto: UpdateStatusOtDto) {

    const { headId, status, statusRemark } = updateStatusOtDto;

    const ot = await this.otRepository.findOne({
      where: { id },
      relations: {
        project: true,
        employee: true,
        head: true,
        approver: true,
      }
    });

    const admin = await User.findOne({
      where: { id: updateStatusOtDto.adminId },
    });

    if (!admin) {// ถ้าไม่ใช่ Admin จะต้องเช็คสถานะและสิทธิ์
      if (!ot) {
        throw new NotFoundException('ot not found');
      }

      if (status == OtStatus.head_approved) {
        if (ot.head.id != headId) {
          throw new BadRequestException("You can't Approver for this ot.")
        }
      }

      if (status == OtStatus.approved) {
        if (ot.approver.id != headId) {
          throw new BadRequestException("You can't Approver for this ot.")
        }
      }
    }

    const queryRunner = this.dataSource.createQueryRunner();

    await queryRunner.connect();
    await queryRunner.startTransaction();
    try {

      const updateData: any = {
        status,
        statusRemark,
        statusDate: new Date(),
      };

      if (admin) {
        updateData.admin = { id: admin.id };
      }

      await queryRunner.manager.update(Ot, ot.id, updateData);

      await queryRunner.commitTransaction();
    } catch (err) {
      await queryRunner.rollbackTransaction();
      console.error(err)
      throw new BadRequestException(err?.message)
    } finally {
      await queryRunner.release();
    }

    const formattedDate = ot?.date
      ? DateTime.fromSQL(ot.date.toString()).toLocal().toFormat('dd/MM/yyyy')
      : null;

    const formattedTimeStart = ot?.timeStart
      ? DateTime.fromISO(ot.timeStart.toString()).toLocal().toFormat('HH:mm')
      : null;

    const formattedTimeEnd = ot?.timeEnd
      ? DateTime.fromISO(ot.timeEnd.toString()).toLocal().toFormat('HH:mm')
      : null;

    if (status == OtStatus.head_approved) {

      //sent email to Approver approver
      await this.mailerService.sendMail({
        to: ot?.approver?.email,
        subject: 'Overtime (OT) Request',
        template: 'ot-approve',
        context: {
          fullname: ot?.employee.fullname,
          project: ot?.project.name,
          date: formattedDate,
          timeStart: formattedTimeStart,
          timeEnd: formattedTimeEnd,
          total: ot?.qtyHour,
          detail: ot?.detail,
          // web: process.env.WEB_URL + '/ot/ot-approval',
          web: process.env.WEB_URL + '/sign-in',
        }
      }).catch((emailErr) => {
        console.error('Failed to send email:', emailErr);
      });
    }

    // Send email to employee when confirm or reject
    const emailTemplate = status === OtStatus.approved || status === OtStatus.head_approved
      ? 'ot-confirm'
      : 'ot-reject';

    this.mailerService.sendMail({
      to: ot.employee?.email,
      subject: 'Overtime (OT) Request',
      template: emailTemplate,
      context: {
        fullname: ot?.employee.fullname,
        project: ot?.project.name,
        date: formattedDate,
        timeStart: formattedTimeStart,
        timeEnd: formattedTimeEnd,
        total: ot?.qtyHour,
        detail: ot?.detail,
        web: '',
      },
    }).catch((emailErr) => {
      console.error('Failed to send email:', emailErr);
    });

    return { message: 'ok' }
  }


  async getTimeOt(body) {

    const employeeId = body.employeeId
    const date = body.date

    let isHoliday = false
    let timeIn = null
    let timeOut = null

    const employee = await this.employeeRepository.findOne({
      relations: ['level'],
      where: {
        id: employeeId
      }
    })

    const getWorkShiftEmployee = await this.employeeService.getWorkShiftEmployee(employeeId, date)

    if (getWorkShiftEmployee) {
      const is_holiday = getWorkShiftEmployee.is_holiday

      isHoliday = is_holiday
      let _time = null
      let otTimeIn = null

      if (is_holiday == true) {
        otTimeIn = getWorkShiftEmployee.workShift.time_in

      } else {
        _time = getWorkShiftEmployee.workShift.time_out

        // ใช้ luxon แปลงเวลาและเพิ่มครึ่งชั่วโมง
        otTimeIn = DateTime.fromFormat(_time, 'HH:mm:ss', { zone: 'UTC' })
          .plus({ minutes: 30 })
          .toFormat('HH:mm:ss');
      }

      //get level
      let qtyDayOtWorkMin = employee?.level?.qtyDayOtWorkMin
      let qtyDayOtOffMin = employee?.level?.qtyDayOtOffMin + 1 //

      let otTimeOut = null
      if (is_holiday == true) {
        //เพิ่ม 5 ชม //รวมพัก
        otTimeOut = DateTime.fromFormat(otTimeIn, 'HH:mm:ss', { zone: 'UTC' })
          .plus({ hours: qtyDayOtOffMin })
          .toFormat('HH:mm:ss');
      } else {
        //เพิ่ม 2 ชม
        otTimeOut = DateTime.fromFormat(otTimeIn, 'HH:mm:ss', { zone: 'UTC' })
          .plus({ hours: qtyDayOtWorkMin })
          .toFormat('HH:mm:ss');
      }

      timeIn = otTimeIn
      timeOut = otTimeOut

    }

    let data = {
      isHoliday: isHoliday,
      timeIn: timeIn,
      timeOut: timeOut,
      // getWorkShiftEmployee: getWorkShiftEmployee,
    }

    return data

  }

}


