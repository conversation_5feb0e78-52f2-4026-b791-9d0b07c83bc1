import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty } from "class-validator";
export class CreateOtDto {

    @IsNotEmpty()
    @ApiProperty()
    readonly date: string;

    @ApiProperty()
    readonly timeStart: string;

    @IsNotEmpty()
    @ApiProperty()
    readonly timeEnd: string;

    @IsNotEmpty()
    @ApiProperty()
    readonly detail: string;

    @IsNotEmpty()
    @ApiProperty()
    readonly employeeId: number;

    @IsNotEmpty()
    @ApiProperty()
    readonly projectId: number;

    @IsNotEmpty()
    @ApiProperty()
    readonly headId: number;
}


