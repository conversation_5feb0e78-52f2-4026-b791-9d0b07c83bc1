import { Column, Entity, Index, OneToMany } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { Exclude, Expose } from "class-transformer";
import { Ot } from "../../ot/entities/ot.entity";
import { OtAir } from "src/ot-air/entities/ot-air.entity";

export enum UserRole {
    ADMIN = 'ADMIN',
    HR = 'HR',
    ADMIN_HR = 'ADMIN_HR',
    ADMIN_OT = 'ADMIN_OT',
    OT = 'OT',
    ADMIN_CLIENT_LIST = 'ADMIN_CLIENT_LIST',
    CLIENT_LIST = 'CLIENT_LIST',
}

@Entity()
export class User extends CustomBaseEntity {
    @Index({ unique: true })
    @Column()
    username: string;

    @Column()
    @Exclude()
    passwordHash: string;

    @Column()
    firstname: string;

    @Column()
    lastname: string;

    @Column("text", { array: true, nullable: true })
    roles: UserRole[];

    @Column({ default: true })
    active: boolean

    @Expose()
    get fullname(): string {
        return `${this.firstname} ${this.lastname}`;
    }

    // @OneToMany(() => PersonalForm, (_) => _.organisedBy)
    // personalFormsOrganise: PersonalForm[];

    @OneToMany(() => Ot, (_) => _.admin)
    ots: Array<Ot>;

    @OneToMany(() => OtAir, (_) => _.admin)
    otAirs: Array<OtAir>;
}
