import { Injectable } from '@nestjs/common';
import { Between, DataSource } from 'typeorm';
import { Employee } from 'src/employee/entities/employee.entity';
import { DateTime } from 'luxon';
import { datetime2string } from 'src/common/utils/DatetimeUtil';
import { Attandance, AttandanceConfirmStatusEnum, AttandanceStatusEnum } from './entities/attandance.entity';
import { groupBy } from 'lodash';
import { Holiday } from 'src/holiday/entities/holiday.entity';

@Injectable()
export class AttandancesService {
  constructor(
    private dataSource: DataSource
  ) { }
  async pullAttendance(year: number, month: number) {

    const start = DateTime.local(year, month).startOf('month')
    const end = DateTime.local(year, month).endOf('month')

    const employees = await Employee.find({
      where: {
        vwAttendance: {
          date: Between(start.toFormat('yyyy-MM-dd'), end.toFormat('yyyy-MM-dd'))
        }
      },
      relations: {
        employeeType: true,
        vwAttendance: true,
        workShift: {
          workShiftTimes: true
        },
        leaves: true,
      }
    })

    const holiday = await this.dataSource
      .createQueryBuilder(Holiday, 'holiday')
      .leftJoinAndSelect('holiday.employeeType', 'employeeType')
      .where("DATE_PART('Year', date) = :year", { year: year })
      .getMany()

    const holidayGroupDate = groupBy(holiday, (e) => {
      return e.date + "#" + e.employeeType.id
    })

    const attandanceForCreate: Attandance[] = [];

    for (const employee of employees) {
      const dayOfMonth = DateTime.local(year, month).daysInMonth

      const attandances: { [key: string]: Attandance } = {};

      for (let index = 1; index <= dayOfMonth; index++) {
        const date = DateTime.local(year, month, index)
        const attandance = Attandance.create({
          workDate: date.toJSDate(),
          // status: AttandanceStatusEnum.ABSENT,
          confirmStatus: AttandanceConfirmStatusEnum.DRAFT,
          employee: { id: employee.id }
        })

        const dateStr = date.toFormat('yyyy-MM-dd')

        attandances[dateStr] = attandance
      }

      const { vwAttendance, workShift } = employee

      const workDayGroup = groupBy(workShift.workShiftTimes, 'day')

      const groupDateTime = groupBy(vwAttendance, function (data) {
        return DateTime.fromJSDate(new Date(data.date)).toLocal().toFormat('yyyy-MM-dd');
      })

      for (const [key, value] of Object.entries(attandances)) {
        if (groupDateTime[key] != undefined) {
          attandances[key].checkIn = groupDateTime[key][0].min
          attandances[key].checkOut = groupDateTime[key][0].max

          const date1 = DateTime.fromFormat(attandances[key].checkIn.toString(), 'HH:mm:ss')
          const date2 = DateTime.fromFormat(attandances[key].checkOut.toString(), 'HH:mm:ss')
          const { minutes } = date2.diff(date1, 'minutes').toObject()

          attandances[key].workedHours = minutes
          
          if (minutes > 0) {
            attandances[key].expectedHours = minutes - 60
          }
        }

        const holiday4emp = key + '#' + employee.employeeType.id
        attandances[key].isHoliday = holidayGroupDate[holiday4emp] != undefined

        const dayOfWeek = DateTime.fromSQL(key).weekday
        attandances[key].isWorkday = workDayGroup[dayOfWeek][0].active
        attandances[key].workStart = workDayGroup[dayOfWeek][0].time_in
        attandances[key].workEnd = workDayGroup[dayOfWeek][0].time_out
      }

      attandanceForCreate.push(...Object.values(attandances))
    }

    await Attandance.save(attandanceForCreate, { chunk: 200 })

    return { message: 'ok'}
  }
}
