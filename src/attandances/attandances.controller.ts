import { Controller, Post, Body } from '@nestjs/common';
import { AttandancesService } from './attandances.service';
import { PullAttandanceDto } from './dto/pull-attandance.dto';

@Controller('attandances')
export class AttandancesController {
  constructor(private readonly attandancesService: AttandancesService) { }

  @Post('/pull')
  pullAttendance(@Body() payload: PullAttandanceDto) {
    return this.attandancesService.pullAttendance(payload.year, payload.month,)
  }
}
